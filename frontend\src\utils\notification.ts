import { ElMessage } from 'element-plus'

/**
 * 全局统一消息通知工具
 * 自动清除重复消息，避免消息叠加问题
 */

// 消息类型定义
type MessageType = 'success' | 'error' | 'warning' | 'info'

// 最后一条消息的记录，用于避免重复
let lastMessage: { type: MessageType; content: string; timestamp: number } | null = null

/**
 * 显示唯一消息（自动清除之前的消息）
 * @param type 消息类型
 * @param message 消息内容
 * @param duration 显示时长（毫秒）
 */
const showUniqueMessage = (type: MessageType, message: string, duration?: number) => {
  const now = Date.now()

  // 如果是相同消息且在1秒内，则忽略
  if (
    lastMessage &&
    lastMessage.type === type &&
    lastMessage.content === message &&
    now - lastMessage.timestamp < 1000
  ) {
    return
  }

  // 清除所有现有消息
  ElMessage.closeAll()

  // 显示新消息
  ElMessage({
    type,
    message,
    duration: duration ?? (type === 'error' ? 4000 : 3000),
    showClose: false,
  })

  // 记录最后一条消息
  lastMessage = { type, content: message, timestamp: now }
}

/**
 * 显示成功消息
 */
export const showSuccessMessage = (message: string, duration?: number) => {
  showUniqueMessage('success', message, duration)
}

/**
 * 显示错误消息
 */
export const showErrorMessage = (message: string, duration?: number) => {
  showUniqueMessage('error', message, duration)
}

/**
 * 显示警告消息
 */
export const showWarningMessage = (message: string, duration?: number) => {
  showUniqueMessage('warning', message, duration)
}

/**
 * 显示信息消息
 */
export const showInfoMessage = (message: string, duration?: number) => {
  showUniqueMessage('info', message, duration)
}

/**
 * 清除所有消息
 */
export const clearAllMessages = () => {
  ElMessage.closeAll()
  lastMessage = null
}

/**
 * 批量替换组件中的 ElMessage 调用
 * 使用示例：
 *
 * // 替换前
 * ElMessage.success('操作成功')
 *
 * // 替换后
 * showSuccessMessage('操作成功')
 */
export const message = {
  success: showSuccessMessage,
  error: showErrorMessage,
  warning: showWarningMessage,
  info: showInfoMessage,
  closeAll: clearAllMessages,
}

// 默认导出，方便使用
export default {
  success: showSuccessMessage,
  error: showErrorMessage,
  warning: showWarningMessage,
  info: showInfoMessage,
  closeAll: clearAllMessages,
}
