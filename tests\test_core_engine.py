"""
核心自动化引擎测试
===============

测试VisaAutomationEngine的配置加载、流程编排和异常处理
"""

from unittest.mock import Mock, patch

import pytest

from app.core.visa_automation_engine import VisaAutomationEngine
from app.data.model import VietnamEVisaApplicant


class TestVisaAutomationEngineInit:
    """自动化引擎初始化测试"""

    def test_engine_initialization_with_real_config(self):
        """测试引擎使用真实配置初始化"""
        # 使用真实的配置加载，不使用Mock
        engine = VisaAutomationEngine()

        # 验证配置已正确加载
        assert isinstance(engine.locators, dict)
        assert isinstance(engine.settings, dict)
        assert "vietnam_evisa_url" in engine.settings

        # 验证locators结构
        if "vietnam_evisa" in engine.locators:
            assert isinstance(engine.locators["vietnam_evisa"], dict)

    def test_engine_initialization_with_missing_config(self):
        """测试配置缺失时的处理"""
        # 只Mock文件系统相关的外部依赖
        with patch("pathlib.Path.is_dir", return_value=False):
            # Mock环境变量，返回适当的默认值
            def mock_load_env_var(key, default):
                if key == "SLOW_MO":
                    return "0"
                return default

            with patch(
                "app.utils.env_loader.load_env_var", side_effect=mock_load_env_var
            ):
                engine = VisaAutomationEngine()

                # 应该有默认设置
                assert engine.settings is not None
                assert engine.locators == {}


class TestVisaAutomationEngineConfiguration:
    """配置加载测试"""

    def test_real_config_loading(self):
        """测试真实配置加载功能"""
        engine = VisaAutomationEngine()

        # 验证必要的配置项
        required_settings = [
            "vietnam_evisa_url",
            "browser",
            "headless",
            "slow_mo",
            "anti_captcha_api_key",
        ]

        for setting in required_settings:
            assert setting in engine.settings

        # 验证配置类型
        assert isinstance(engine.settings["headless"], bool)
        assert isinstance(engine.settings["slow_mo"], int)
        assert isinstance(engine.settings["browser"], str)


class TestVisaAutomationEngineMainFlow:
    """主要流程测试 - 减少Mock，专注于业务逻辑"""

    @pytest.fixture
    def real_engine(self):
        """使用真实配置的引擎实例"""
        return VisaAutomationEngine()

    @pytest.fixture
    def sample_applicant(self):
        """示例申请人"""
        return VietnamEVisaApplicant(
            passport_number="*********",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="China",
            sex="MALE",
        )

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_run_vietnam_evisa_browser_initialization(
        self, mock_playwright, real_engine, sample_applicant
    ):
        """测试浏览器初始化流程 - 只Mock外部依赖"""
        # 只Mock Playwright（外部依赖）
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # Mock基本的浏览器对象
        mock_browser = Mock()
        mock_context = Mock()
        mock_page = Mock()
        mock_page.is_closed.return_value = False
        mock_browser.is_connected.return_value = True

        # Mock浏览器启动 - 这是外部依赖
        with patch(
            "app.core.visa_automation_engine.launch_form_browser"
        ) as mock_launch:
            mock_launch.return_value = (mock_browser, mock_context, mock_page)

            # Mock页面导航失败，触发异常处理
            mock_page.goto.side_effect = Exception("Navigation failed")

            # 执行测试
            result = real_engine.run_vietnam_evisa_step1(sample_applicant)

            # 验证结果
            assert result is False

            # 验证浏览器启动被调用
            mock_launch.assert_called()

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_run_vietnam_evisa_configuration_validation(
        self, mock_playwright, real_engine, sample_applicant
    ):
        """测试配置验证逻辑"""
        # Mock Playwright
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # 临时修改配置为无效配置
        original_url = real_engine.settings.get("vietnam_evisa_url")
        real_engine.settings["vietnam_evisa_url"] = ""

        try:
            # 执行测试
            result = real_engine.run_vietnam_evisa_step1(sample_applicant)

            # 验证结果
            assert result is False

        finally:
            # 恢复配置
            real_engine.settings["vietnam_evisa_url"] = original_url

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_run_vietnam_evisa_retry_mechanism(
        self, mock_playwright, real_engine, sample_applicant
    ):
        """测试重试机制"""
        # Mock Playwright
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # Mock浏览器启动失败
        with patch(
            "app.core.visa_automation_engine.launch_form_browser"
        ) as mock_launch:
            mock_launch.side_effect = Exception("Browser launch failed")

            # 执行测试
            result = real_engine.run_vietnam_evisa_step1(sample_applicant)

            # 验证结果
            assert result is False

            # 验证重试了最大次数
            assert mock_launch.call_count == 3  # 最大重试3次


class TestVisaAutomationEngineErrorHandling:
    """错误处理测试"""

    @pytest.fixture
    def real_engine(self):
        """使用真实配置的引擎实例"""
        return VisaAutomationEngine()

    @pytest.fixture
    def sample_applicant(self):
        """示例申请人"""
        return VietnamEVisaApplicant(
            passport_number="*********",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="China",
            sex="MALE",
        )

    def test_missing_applicant_data(self, real_engine):
        """测试缺少申请人数据"""
        # 创建不完整的申请人数据
        incomplete_applicant = VietnamEVisaApplicant(
            passport_number="",  # 缺少护照号
            chinese_name="",
            surname="",
            given_name="",
            dob="",
            nationality="",
            sex="",
        )

        # 测试处理不完整数据
        # 这里使用真实的业务逻辑验证
        assert incomplete_applicant.passport_number == ""
        assert incomplete_applicant.chinese_name == ""

    def test_applicant_data_validation(self, sample_applicant):
        """测试申请人数据验证"""
        # 验证必要字段
        assert sample_applicant.passport_number != ""
        assert sample_applicant.chinese_name != ""
        assert sample_applicant.surname != ""
        assert sample_applicant.given_name != ""
        assert sample_applicant.dob != ""
        assert sample_applicant.nationality != ""
        assert sample_applicant.sex != ""

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_resource_cleanup_on_exception(
        self, mock_playwright, real_engine, sample_applicant
    ):
        """测试异常情况下的资源清理"""
        # Mock Playwright
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # Mock浏览器对象
        mock_browser = Mock()
        mock_context = Mock()
        mock_page = Mock()

        with patch(
            "app.core.visa_automation_engine.launch_form_browser"
        ) as mock_launch:
            mock_launch.return_value = (mock_browser, mock_context, mock_page)

            # Mock页面操作失败
            mock_page.goto.side_effect = Exception("Page load failed")

            # 执行测试
            result = real_engine.run_vietnam_evisa_step1(sample_applicant)

            # 验证结果
            assert result is False

            # 验证资源清理被调用
            mock_p_context.stop.assert_called()
