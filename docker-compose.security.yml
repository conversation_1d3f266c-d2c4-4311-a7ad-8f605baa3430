# 🔒 安全增强配置 - 2025年OWASP Docker安全标准
# 使用方式：docker compose -f docker-compose.yml -f docker-compose.security.yml up -d
#
# 🎯 安全特性：
# - 只读文件系统 + tmpfs挂载
# - 严格的文件权限
# - 最小权限原则
# - 防止权限提升

services:
  # 后端服务 - 安全配置
  visa-automator:
    read_only: true  # 🔒 只读文件系统
    security_opt:
      - "no-new-privileges:true"  # 🔒 防止权限提升
    cap_drop:
      - ALL  # 🔒 移除所有Linux内核能力
    cap_add:
      - CHOWN  # 只添加必要的能力
    tmpfs:
      # 🔧 为需要写入的目录提供tmpfs挂载
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=200m
      - /tmp:noexec,nosuid,size=100m
    volumes:
      # 🔧 持久化存储的目录使用volume挂载
      - screenshots_data:/app/screenshots:rw
      - downloads_data:/app/downloads:rw
      - prefs_data:/app/.prefs:rw
      - payment_screenshots_data:/app/payment_screenshots:rw
      - test_data:/app/test_data:rw
      - results_data:/app/results:rw

  # Celery工作者 - 安全配置
  celery-worker:
    read_only: true  # 🔒 只读文件系统
    security_opt:
      - "no-new-privileges:true"  # 🔒 防止权限提升
    cap_drop:
      - ALL  # 🔒 移除所有Linux内核能力
    cap_add:
      - CHOWN  # 只添加必要的能力
    tmpfs:
      # 🔧 为需要写入的目录提供tmpfs挂载
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=200m
      - /tmp:noexec,nosuid,size=100m
    volumes:
      # 🔧 持久化存储的目录使用volume挂载（与后端共享）
      - screenshots_data:/app/screenshots:rw
      - downloads_data:/app/downloads:rw
      - prefs_data:/app/.prefs:rw
      - payment_screenshots_data:/app/payment_screenshots:rw
      - test_data:/app/test_data:rw
      - results_data:/app/results:rw

  # 邮件轮询服务 - 安全配置
  email-polling:
    read_only: true  # 🔒 只读文件系统
    security_opt:
      - "no-new-privileges:true"  # 🔒 防止权限提升
    cap_drop:
      - ALL  # 🔒 移除所有Linux内核能力
    cap_add:
      - CHOWN  # 只添加必要的能力
    tmpfs:
      # 🔧 为需要写入的目录提供tmpfs挂载
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=100m
      - /tmp:noexec,nosuid,size=100m
    volumes:
      # 🔧 持久化存储的目录使用volume挂载（与其他服务共享）
      - downloads_data:/app/downloads:rw

# 🔧 定义持久化存储卷
volumes:
  screenshots_data:
    driver: local
  downloads_data:
    driver: local
  prefs_data:
    driver: local
  payment_screenshots_data:
    driver: local
  test_data:
    driver: local
  results_data:
    driver: local
