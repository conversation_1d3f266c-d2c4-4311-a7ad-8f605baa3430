"""
Repository层单元测试
=================

测试Repository模式的CRUD操作、查询逻辑和异常处理
"""

import uuid

import pytest
from sqlalchemy.exc import IntegrityError

from app.repositories.applicant_repository import ApplicantRepository
from app.repositories.application_repository import ApplicationRepository
from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestApplicantRepository:
    """申请人仓库测试"""

    @pytest.mark.asyncio
    async def test_create_applicant(self, db_session):
        """测试创建申请人"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建申请人
        repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        created_applicant = await repo.create(applicant)

        assert created_applicant.id is not None
        assert created_applicant.user_id == user.id

    @pytest.mark.asyncio
    async def test_find_applicant_by_passport(self, db_session):
        """测试根据护照号查找申请人"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建申请人
        repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "*********"}
        )
        await repo.create(applicant)

        # 查找申请人
        found_applicant = await repo.get_by_passport_number("*********")

        assert found_applicant is not None
        assert found_applicant.passport_number == "*********"

    @pytest.mark.asyncio
    async def test_find_applicants_by_user(self, db_session):
        """测试根据用户ID查找申请人"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建多个申请人
        repo = ApplicantRepository(db_session)
        applicant1 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "E11111111"}
        )
        applicant2 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "E22222222"}
        )
        await repo.create(applicant1)
        await repo.create(applicant2)

        # 查找用户的所有申请人
        applicants = await repo.get_by_user_id(user.id)

        assert len(applicants) == 2
        user_ids = [applicant.user_id for applicant in applicants]
        assert all(uid == user.id for uid in user_ids)

    @pytest.mark.asyncio
    async def test_update_applicant(self, db_session):
        """测试更新申请人"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建申请人
        repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "given_name": "John"}
        )
        created_applicant = await repo.create(applicant)

        # 更新申请人
        created_applicant.given_name = "Jane"
        updated_applicant = await repo.update(created_applicant)

        assert updated_applicant.given_name == "Jane"

    @pytest.mark.asyncio
    async def test_delete_applicant(self, db_session):
        """测试删除申请人"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建申请人
        repo = ApplicantRepository(db_session)
        applicant1 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "E11111111"}
        )
        applicant2 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "E22222222"}
        )
        created_applicant1 = await repo.create(applicant1)
        await repo.create(applicant2)

        # 删除一个申请人
        deleted = await repo.delete(created_applicant1.id)
        assert deleted is True

        # 验证删除
        remaining_applicants = await repo.get_by_user_id(user.id)
        assert len(remaining_applicants) == 1
        assert remaining_applicants[0].passport_number == "E22222222"

    @pytest.mark.asyncio
    async def test_applicant_exists(self, db_session):
        """测试检查申请人是否存在"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建申请人
        repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        created_applicant = await repo.create(applicant)

        # 测试存在的申请人
        existing_applicant = await repo.get_by_id(created_applicant.id)
        assert existing_applicant is not None

        # 测试不存在的申请人
        non_existing_applicant = await repo.get_by_id(uuid.uuid4())
        assert non_existing_applicant is None


class TestOrderRepository:
    """订单仓库测试"""

    @pytest.mark.asyncio
    async def test_create_order(self, db_session):
        """测试创建订单"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单
        repo = OrderRepository(db_session)
        order = TestDataFactory.create_order_data({"user_id": user.id})
        created_order = await repo.create(order)

        assert created_order.id is not None
        assert created_order.user_id == user.id

    @pytest.mark.asyncio
    async def test_find_order_by_order_no(self, db_session):
        """测试根据订单号查找订单"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        order_no = "VN20250621TEST001"

        # 创建订单
        repo = OrderRepository(db_session)
        order = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": order_no}
        )
        await repo.create(order)

        # 查找订单
        found_order = await repo.get_by_order_no(order_no)

        assert found_order is not None
        assert found_order.order_no == order_no

    @pytest.mark.asyncio
    async def test_find_orders_by_user(self, db_session):
        """测试根据用户ID查找订单"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建多个订单
        repo = OrderRepository(db_session)
        order1 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": "VN20250621TEST001"}
        )
        order2 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": "VN20250621TEST002"}
        )
        await repo.create(order1)
        await repo.create(order2)

        # 查找用户的所有订单
        orders = await repo.get_by_user_id(user.id)

        assert len(orders) == 2
        user_ids = [order.user_id for order in orders]
        assert all(uid == user.id for uid in user_ids)

    @pytest.mark.asyncio
    async def test_update_order_status(self, db_session):
        """测试更新订单状态"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单
        repo = OrderRepository(db_session)
        order = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_status": "created"}
        )
        created_order = await repo.create(order)

        # 更新订单状态 - 使用可用的状态值
        success = await repo.update_status(
            created_order.order_no, OrderStatus.CANCELLED
        )

        assert success is True

        # 重新获取订单验证状态更新
        updated_order = await repo.get_by_order_no(created_order.order_no)
        assert updated_order is not None
        assert updated_order.order_status == "cancelled"

    @pytest.mark.asyncio
    async def test_find_orders_by_status(self, db_session):
        """测试根据状态查找订单"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建不同状态的订单
        repo = OrderRepository(db_session)
        order1 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_status": "created"}
        )
        order2 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_status": "processing"}
        )
        await repo.create(order1)
        await repo.create(order2)

        # 查找特定状态的订单 - 需要提供user_id进行权限控制
        created_orders = await repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=user.id
        )

        assert len(created_orders) >= 1
        assert all(order.order_status == "created" for order in created_orders)

    @pytest.mark.asyncio
    async def test_delete_order(self, db_session):
        """测试删除订单"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单
        repo = OrderRepository(db_session)
        order1 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": "VN20250621TEST001"}
        )
        order2 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": "VN20250621TEST002"}
        )
        created_order1 = await repo.create(order1)
        await repo.create(order2)

        # 删除一个订单
        deleted = await repo.delete(created_order1.id)
        assert deleted is True

        # 验证删除
        remaining_orders = await repo.get_by_user_id(user.id)
        assert len(remaining_orders) == 1
        assert remaining_orders[0].order_no == "VN20250621TEST002"


class TestApplicationRepository:
    """申请仓库测试"""

    @pytest.mark.asyncio
    async def test_create_application(self, db_session):
        """测试创建申请"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单
        order_repo = OrderRepository(db_session)
        order = TestDataFactory.create_order_data({"user_id": user.id})
        created_order = await order_repo.create(order)

        # 创建申请人
        applicant_repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        created_applicant = await applicant_repo.create(applicant)

        # 创建申请
        app_repo = ApplicationRepository(db_session)
        application = TestDataFactory.create_application_data(
            {
                "user_id": user.id,
                "order_id": created_order.id,
                "applicant_id": created_applicant.id,
            }
        )
        created_application = await app_repo.create(application)

        assert created_application.id is not None
        assert created_application.user_id == user.id

    @pytest.mark.asyncio
    async def test_find_application_by_order(self, db_session):
        """测试根据订单ID查找申请"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单
        order_repo = OrderRepository(db_session)
        order = TestDataFactory.create_order_data({"user_id": user.id})
        created_order = await order_repo.create(order)

        # 创建申请人
        applicant_repo = ApplicantRepository(db_session)
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        created_applicant = await applicant_repo.create(applicant)

        # 创建申请
        app_repo = ApplicationRepository(db_session)
        application = TestDataFactory.create_application_data(
            {
                "user_id": user.id,
                "order_id": created_order.id,
                "applicant_id": created_applicant.id,
            }
        )
        await app_repo.create(application)

        # 查找申请
        found_application = await app_repo.get_by_order_id(created_order.id)

        assert found_application is not None
        assert found_application.order_id == created_order.id

    @pytest.mark.asyncio
    async def test_find_applications_by_user(self, db_session):
        """测试根据用户ID查找申请"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建订单和申请人
        order_repo = OrderRepository(db_session)
        applicant_repo = ApplicantRepository(db_session)
        app_repo = ApplicationRepository(db_session)

        # 创建多个申请
        for i in range(2):
            order = TestDataFactory.create_order_data(
                {"user_id": user.id, "order_no": f"VN20250621TEST00{i + 1}"}
            )
            created_order = await order_repo.create(order)

            applicant = TestDataFactory.create_applicant_data(
                {"user_id": user.id, "passport_number": f"E1111111{i}"}
            )
            created_applicant = await applicant_repo.create(applicant)

            application = TestDataFactory.create_application_data(
                {
                    "user_id": user.id,
                    "order_id": created_order.id,
                    "applicant_id": created_applicant.id,
                }
            )
            await app_repo.create(application)

        # 查找用户的所有申请
        applications = await app_repo.get_applications_by_user_id(user.id)

        assert len(applications) == 2
        user_ids = [app.user_id for app in applications]
        assert all(uid == user.id for uid in user_ids)

    @pytest.mark.asyncio
    async def test_complex_repository_operations(self, db_session):
        """测试复杂的Repository操作"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建多个订单
        order_repo = OrderRepository(db_session)
        for i in range(3):
            order = TestDataFactory.create_order_data(
                {"user_id": user.id, "order_no": f"VN20250621TEST00{i + 1}"}
            )
            await order_repo.create(order)

        # 分页查询
        orders_page1 = await order_repo.get_by_user_id(user.id, limit=2, offset=0)
        orders_page2 = await order_repo.get_by_user_id(user.id, limit=2, offset=2)

        assert len(orders_page1) == 2
        assert len(orders_page2) == 1

    @pytest.mark.asyncio
    async def test_repository_error_handling(self, db_session):
        """测试Repository错误处理"""
        repo = ApplicantRepository(db_session)
        non_existent_id = uuid.uuid4()

        # 测试查找不存在的记录
        applicant = await repo.get_by_id(non_existent_id)
        assert applicant is None

        # 测试删除不存在的记录
        deleted = await repo.delete(non_existent_id)
        assert deleted is False


class TestRepositoryErrorHandling:
    """Repository错误处理测试"""

    @pytest.mark.asyncio
    async def test_integrity_error_handling(self, db_session):
        """测试完整性约束错误处理"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 测试唯一约束：同一用户不能有相同护照号的申请人
        repo = ApplicantRepository(db_session)
        applicant1 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": "*********"}
        )
        created_applicant1 = await repo.create(applicant1)
        assert created_applicant1.id is not None

        # 尝试创建相同护照号的申请人（应该失败）
        applicant2 = TestDataFactory.create_applicant_data(
            {
                "user_id": user.id,
                "passport_number": "*********",  # 相同护照号
            }
        )

        # 预期会抛出IntegrityError
        with pytest.raises(IntegrityError):
            await repo.create(applicant2)

    @pytest.mark.asyncio
    async def test_nonexistent_id_handling(self, db_session):
        """测试处理不存在的ID"""
        repo = ApplicantRepository(db_session)
        nonexistent_id = uuid.uuid4()

        # 测试更新不存在的记录
        nonexistent_applicant = await repo.get_by_id(nonexistent_id)
        assert nonexistent_applicant is None

        # 测试删除不存在的记录
        delete_result = await repo.delete(nonexistent_id)
        assert delete_result is False


class TestRepositoryPagination:
    """Repository分页测试"""

    @pytest.mark.asyncio
    async def test_pagination_consistency(self, db_session):
        """测试分页一致性"""
        # 创建用户
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        # 创建多个申请人
        repo = ApplicantRepository(db_session)
        total_count = 5
        for i in range(total_count):
            applicant = TestDataFactory.create_applicant_data(
                {"user_id": user.id, "passport_number": f"E12345{i:03d}"}
            )
            await repo.create(applicant)

        # 测试分页
        page1 = await repo.get_by_user_id(user.id, limit=2, offset=0)
        page2 = await repo.get_by_user_id(user.id, limit=2, offset=2)
        page3 = await repo.get_by_user_id(user.id, limit=2, offset=4)

        assert len(page1) == 2
        assert len(page2) == 2
        assert len(page3) == 1

        # 验证总数
        total_count_from_repo = await repo.count_by_user_id(user.id)
        assert total_count_from_repo == total_count
