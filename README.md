# 越南签证自动化系统 (Vietnam Visa Automation System)

基于 **Vue 3 + TypeScript + Element Plus** 和 **FastAPI** 的现代化签证申请自动化平台。

## 🚀 项目特点

- **前端**: Vue 3.5 + TypeScript + Element Plus + Pinia + Vite
- **后端**: FastAPI + PostgreSQL + 异步处理
- **自动化**: OCR 识别 + 表单填写 + 文件处理
- **现代化**: 组件化开发 + 类型安全 + 热重载

## 📋 系统要求

### 开发环境

- **Node.js**: >= 20.16.0 (推荐 20.16.0 LTS)
- **npm**: >= 9.0.0
- **Python**: >= 3.9
- **PostgreSQL**: >= 13

### 推荐工具

- **IDE**: VS Code / WebStorm
- **浏览器**: Chrome >= 90 / Edge >= 90

## 🛠️ 安装与启动

### 1. 克隆项目

```bash
git clone <repository-url>
cd visa_automator
```

### 2. 后端启动 (FastAPI)

```bash
# 创建Python虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate
# 激活虚拟环境 (macOS/Linux)
source venv/bin/activate

# 安装依赖
pip install -e .

# 启动后端服务
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload
```

后端服务将在 `http://localhost:8000` 启动

### 3. 前端启动 (Vue 3)

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install


# 启动开发服务器
npm run dev

```

前端服务将在 `http://localhost:5173` 启动

## 📦 构建与部署

### 前端构建

```bash
cd frontend

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建生产版本
npm run build
```

构建产物将输出到 `static_files/` 目录

### 后端部署

```bash
# 使用 Docker
docker-compose up -d

# 或直接启动
uvicorn api_main:app --host 0.0.0.0 --port 8000
```

## 🏗️ 项目结构

```
visa_automator/
├── frontend/                    # Vue 3 前端项目
│   ├── src/
│   │   ├── api/                # API 接口层
│   │   ├── components/         # 可复用组件
│   │   ├── composables/        # 组合式函数
│   │   ├── stores/             # Pinia 状态管理
│   │   ├── types/              # TypeScript 类型
│   │   ├── utils/              # 工具函数
│   │   ├── views/              # 页面组件
│   │   └── constants/          # 常量定义
│   ├── vite.config.ts          # Vite 配置
│   └── package.json            # 前端依赖
├── backend/                    # FastAPI 后端
├── legacy_frontend/            # 旧版前端 (可删除)
├── docs/                       # 项目文档
├── pyproject.toml              # Python 依赖和项目配置
├── docker-compose.yml          # Docker 配置
└── README.md                   # 项目说明
```

## 🔧 开发命令

### 前端开发

```bash
cd frontend

# 开发服务器 (热重载)
npm run dev

# 代码质量检查 (只检查，不修复)
npm run check           # 检查所有
npm run lint:check      # ESLint 检查
npm run style:check     # Stylelint 检查
npm run format:check    # Prettier 检查
npm run type-check      # TypeScript 检查

# 代码修复 (自动修复问题)
npm run fix             # 修复所有
npm run lint:fix        # ESLint 修复
npm run style:fix       # Stylelint 修复
npm run format:fix      # Prettier 修复

# 单元测试
npm run test:unit

# 构建预览
npm run preview

# 清理构建产物和缓存
npm run clean
```

### 后端开发

```bash
# 开发模式启动 (自动重载)
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload

# 代码质量检查
ruff check .            # 代码检查
ruff format --check .   # 格式检查
mypy .                  # 类型检查

# 代码修复
ruff check --fix .      # 修复代码问题
ruff format .           # 格式化代码

# 运行测试
pytest

# 数据库迁移
alembic upgrade head

# 全项目检查
python lint_all.py
```

### 项目级命令

```bash
# 安装所有依赖
npm run install:all

# 清理所有构建产物
npm run clean

# 运行 pre-commit 检查
npm run pre-commit
```

## 🌟 核心功能

- ✅ **智能表单**: Element Plus + 实时验证
- ✅ **OCR 识别**: 护照/身份证自动识别
- ✅ **文件上传**: 拖拽上传 + 格式验证
- ✅ **状态管理**: Pinia 全局状态
- ✅ **类型安全**: 完整 TypeScript 支持
- ✅ **响应式设计**: 移动端适配

## 📚 技术文档

详细文档请查看 `docs/` 目录：

- [架构设计](docs/frontend-architecture-proposal.md)
- [重构指南](docs/refactor-roadmap.md)
- [Element Plus 分析](docs/element-plus-analysis.md)
- [框架对比](docs/framework-comparison.md)

## 🐳 Docker 部署 - 2025年安全架构

### 🔥 **架构特性**

- ✅ **非root用户运行**：所有服务以非root用户运行，符合安全最佳实践
- ✅ **环境变量强制验证**：防止配置错误，提高部署可靠性
- ✅ **开发环境分离**：使用 `docker-compose.override.yml` 实现环境隔离
- ✅ **多容器扩展支持**：支持水平扩展，适合批量任务处理

### 开发环境（端口8001）

```bash
# 启动开发环境（自动使用override配置）
docker-compose up -d

# 访问地址
# 后端API: http://localhost:8001
# API文档: http://localhost:8001/docs

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 测试环境（端口8000）

```bash
# 启动测试环境（生产级安全配置）
docker-compose -f docker-compose.yml up -d --build

# 访问地址（通过frp暴露）
# 应用入口: http://localhost:8000

# 扩展 Celery Worker 数量（批量任务处理）
docker-compose up -d --scale celery-worker=10

# 重建容器
docker-compose up --build -d
```

### 🚀 **多容器扩展**

```bash
# 批量任务处理扩展
docker-compose up -d --scale celery-worker=10

# 高并发API扩展
docker-compose up -d --scale visa-automator=3

# 组合扩展（生产环境推荐）
docker-compose up -d --scale visa-automator=3 --scale celery-worker=10
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**

   ```bash
   # 更改前端端口
   npm run dev -- --port 3000

   # 更改后端端口
   uvicorn api_main:app --port 8080
   ```

2. **依赖安装失败**

   ```bash
   # 清除缓存
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript 错误**
   ```bash
   # 重新生成类型文件
   npm run type-check
   ```

## 🤝 开发规范

### 代码质量工具

**前端工具链**:

- **ESLint**: JavaScript/TypeScript 代码检查 (eslint.config.ts)
- **Prettier**: 代码格式化 (.prettierrc.json)
- **Stylelint**: CSS/SCSS 样式检查 (.stylelintrc.cjs)
- **TypeScript**: 类型检查 (tsconfig.json)

**后端工具链**:

- **Ruff**: Python 代码检查和格式化
- **MyPy**: Python 类型检查

**安全扫描工具 (现代化三件套)**:

- **TruffleHog**: 秘密检测和验证 (替代 detect-secrets)
- **Semgrep**: 代码安全分析 (替代 Bandit)
- **pip-audit**: 依赖安全漏洞扫描

**自动化工具**:

- **pre-commit**: Git 提交前快速检查 (增量扫描)
- **GitHub Actions**: CI/CD 全面安全扫描 (全量扫描)
- **lint-staged**: 只检查暂存文件，提升性能

### 开发流程

- **代码风格**: 自动格式化 + 严格检查
- **提交规范**: Conventional Commits
- **分支策略**: Git Flow
- **测试覆盖**: >= 80%
- **类型安全**: 100% TypeScript 覆盖

## 📝 更新日志

### v2.0.0 (2024-01-XX)

- ✨ 全新 Vue 3 + TypeScript 架构
- ✨ Element Plus UI 组件库
- ✨ Pinia 状态管理
- ⚡ Vite 构建工具
- 🎨 现代化 UI 设计

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 👥 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: [<EMAIL>]
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
