"""
单设备登录会话管理器
简洁可靠的Redis方案，存储用户当前有效token标识（只存jti，安全高效）
"""

import logging
import os

import jwt
import redis.asyncio as aioredis

# Set up logging
logger = logging.getLogger(__name__)


class SingleDeviceSessionManager:
    """单设备登录会话管理器（只存jti，安全高效）"""

    def __init__(self):
        """初始化异步Redis连接"""
        redis_host = os.environ.get("REDIS_HOST", "localhost")
        self.redis_client = aioredis.Redis(
            host=redis_host,
            port=6379,
            db=2,  # 使用DB2，避免与Celery冲突
            decode_responses=True,
        )
        self.key_prefix = "single_device_session:"
        self.session_expire = 7200  # 2小时过期
        # 🔧 修复：使用统一的SECRET_KEY环境变量，与auth.py保持一致
        self.jwt_secret = os.environ.get("SECRET_KEY", "your-very-secret-key")

    def _get_user_key(self, user_id: str) -> str:
        """获取用户的Redis键"""
        return f"{self.key_prefix}{user_id}"

    async def store_user_token(self, user_id: str, token: str) -> bool:
        """
        存储用户的当前有效jti（只存jti，不存整个token）
        新token会覆盖旧jti，实现单设备登录
        """
        try:
            key = self._get_user_key(user_id)
            jti = self._extract_jti_from_token(token)
            if not jti:
                logger.error("❌ token中未提取到jti")
                return False
            await self.redis_client.setex(key, self.session_expire, jti)
            logger.info(f"✅ 成功存储用户{user_id}的jti: {jti[:8]}...")
            return True
        except Exception as e:
            logger.error(f"❌ 存储用户jti失败: {e}")
            return False

    async def validate_user_token(self, user_id: str, token: str) -> bool:
        """
        验证用户token的jti是否为当前有效jti
        🔧 优化：增强错误处理和Redis连接检查
        """
        try:
            # 🔧 优化：检查Redis连接状态
            try:
                await self.redis_client.ping()
            except Exception as redis_error:
                logger.error(f"❌ Redis连接失败: {redis_error}")
                # Redis不可用时，允许通过JWT验证（降级策略）
                logger.warning("⚠️ Redis不可用，使用JWT降级验证")
                jti = self._extract_jti_from_token(token)
                return jti is not None

            key = self._get_user_key(user_id)
            stored_jti = await self.redis_client.get(key)

            if stored_jti is None:
                logger.warning(f"🔍 用户{user_id}在Redis中没有找到会话")
                # 🔧 优化：会话不存在时，检查token是否有效
                jti = self._extract_jti_from_token(token)
                if jti:
                    # Token有效但会话不存在，重新存储会话
                    logger.info(f"🔄 重新存储用户{user_id}的会话，jti: {jti[:8]}...")
                    await self.store_user_token(user_id, token)
                    return True
                return False

            jti = self._extract_jti_from_token(token)
            if not jti:
                logger.error(f"❌ 无法从token中提取jti，用户: {user_id}")
                return False

            is_valid = stored_jti == jti
            if is_valid:
                logger.info(f"✅ 用户{user_id}会话验证通过，jti: {jti[:8]}...")
                # 🔧 优化：刷新会话过期时间
                await self.redis_client.expire(key, self.session_expire)
            else:
                logger.warning(
                    f"⚠️ 用户{user_id}会话失效，stored_jti: {stored_jti[:8]}..., current_jti: {jti[:8]}..."
                )

            return is_valid
        except Exception as e:
            logger.error(f"❌ 验证用户jti失败: {e}")
            return False

    def _extract_jti_from_token(self, token: str) -> str | None:
        """从JWT中提取jti（安全解码）"""
        try:
            # 🔐 修复JWT安全风险 - 使用完整验证而不是跳过签名验证
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=["HS256"],
                audience=["fastapi-users:auth"],  # 🔧 修复：添加audience验证
                options={"verify_exp": False},  # 仅跳过过期验证，保持签名验证
            )
            logger.debug(f"[jti-debug] payload: {payload}")
            return payload.get("jti")
        except jwt.InvalidTokenError:
            logger.error("❌ JWT token无效或签名验证失败")
            return None
        except Exception as e:
            logger.error(f"❌ 提取jti失败: {e}")
            return None

    async def remove_user_token(self, user_id: str) -> None:
        """删除用户的Redis会话（登出/失效）"""
        key = self._get_user_key(user_id)
        await self.redis_client.delete(key)


# 全局实例
session_manager = SingleDeviceSessionManager()
