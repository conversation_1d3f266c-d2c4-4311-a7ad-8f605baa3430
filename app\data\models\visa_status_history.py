import uuid

from sqlalchemy import Column, DateTime, ForeignKey, Index, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class VisaStatusHistory(Base):
    __tablename__ = "visa_status_history"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="流转历史主键"
    )
    # 🔐 架构修复：添加user_id以简化权限控制
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        doc="用户外键（权限控制）",
    )
    application_id = Column(
        UUID(as_uuid=True),
        ForeignKey("application.id", ondelete="CASCADE"),
        nullable=False,
        doc="申请外键",
    )
    # 🔐 架构修复：添加order_id以完善订单级别状态追踪
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("order.id", ondelete="CASCADE"),
        nullable=False,
        doc="订单外键（订单级别状态追踪）",
    )
    # 🔥 简化后的字段结构（与数据库同步）
    visa_status = Column(String(32), nullable=False, doc="签证状态")
    application_number = Column(
        String(64), nullable=True, doc="申请编号（增强数据一致性）"
    )
    operator = Column(String(64), doc="操作人")
    remark = Column(String(256), doc="备注")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="更新时间",
    )

    # 🔐 权限控制和性能优化索引
    __table_args__ = (
        Index("ix_visa_status_history_app_status", "application_id", "visa_status"),
    )
