/**
 * Pinia Store单元测试
 * ==================
 *
 * 测试Pinia状态管理store的功能
 */

import { createPinia, setActivePinia } from 'pinia'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ref } from 'vue'
import { createTestFormData } from './test-utils'

// Mock API calls
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
}

vi.mock('@/api/request', () => ({
  api: mockApi,
}))

// Mock auth store
const createMockAuthStore = () => {
  const user = ref(null)
  const token = ref('')
  const isAuthenticated = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const login = vi.fn()
  const logout = vi.fn()
  const register = vi.fn()
  const checkAuth = vi.fn()
  const clearError = vi.fn()

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    register,
    checkAuth,
    clearError,
  }
}

// Mock visa form store
const createMockVisaFormStore = () => {
  const formData = ref({
    personalInfo: {
      surname: '',
      given_name: '',
      chinese_name: '',
      sex: '',
      dob: '',
      place_of_birth: '',
      nationality: '',
      religion: '',
    },
    passportInfo: {
      passport_number: '',
      date_of_issue: '',
      place_of_issue: '',
      passport_expiry: '',
      passport_type: 'Ordinary passport',
    },
    contactInfo: {
      email: '',
      telephone_number: '',
      permanent_address: '',
      contact_address: '',
    },
    visaInfo: {
      visa_entry_type: '',
      visa_validity_duration: '',
      visa_start_date: '',
      intended_entry_gate: '',
      purpose_of_entry: 'Tourist',
    },
  })

  const isDirty = ref(false)
  const isSubmitting = ref(false)
  const lastSaved = ref<Date | null>(null)

  const updatePersonalInfo = vi.fn()
  const updatePassportInfo = vi.fn()
  const updateContactInfo = vi.fn()
  const updateVisaInfo = vi.fn()
  const resetForm = vi.fn()
  const validateForm = vi.fn()
  const saveForm = vi.fn()
  const loadForm = vi.fn()

  return {
    formData,
    isDirty,
    isSubmitting,
    lastSaved,
    updatePersonalInfo,
    updatePassportInfo,
    updateContactInfo,
    updateVisaInfo,
    resetForm,
    validateForm,
    saveForm,
    loadForm,
  }
}

// Mock submission store
const createMockSubmissionStore = () => {
  const submissions = ref([])
  const currentSubmission = ref(null)
  const isSubmitting = ref(false)
  const error = ref<string | null>(null)

  const submitApplication = vi.fn()
  const getSubmissions = vi.fn()
  const getSubmissionById = vi.fn()
  const cancelSubmission = vi.fn()
  const clearError = vi.fn()

  return {
    submissions,
    currentSubmission,
    isSubmitting,
    error,
    submitApplication,
    getSubmissions,
    getSubmissionById,
    cancelSubmission,
    clearError,
  }
}

// Mock notification store
const createMockNotificationStore = () => {
  const notifications = ref([])
  const unreadCount = ref(0)

  const addNotification = vi.fn()
  const markAsRead = vi.fn()
  const clearNotifications = vi.fn()
  const showSuccess = vi.fn()
  const showError = vi.fn()
  const showWarning = vi.fn()
  const showInfo = vi.fn()

  return {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    clearNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  }
}

describe('Auth Store', () => {
  let authStore: ReturnType<typeof createMockAuthStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    authStore = createMockAuthStore()
  })

  describe('State Management', () => {
    it('should initialize with default state', () => {
      expect(authStore.user.value).toBeNull()
      expect(authStore.token.value).toBe('')
      expect(authStore.isAuthenticated.value).toBe(false)
      expect(authStore.isLoading.value).toBe(false)
      expect(authStore.error.value).toBeNull()
    })
  })

  describe('Authentication Actions', () => {
    it('should handle successful login', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
      }
      const mockToken = 'mock-jwt-token'

      authStore.login.mockResolvedValue({
        user: mockUser,
        token: mockToken,
      })

      const result = await authStore.login('<EMAIL>', 'password')

      expect(authStore.login).toHaveBeenCalledWith('<EMAIL>', 'password')
      expect(result).toEqual({ user: mockUser, token: mockToken })
    })

    it('should handle login errors', async () => {
      const errorMessage = 'Invalid credentials'
      authStore.login.mockRejectedValue(new Error(errorMessage))

      try {
        await authStore.login('<EMAIL>', 'wrongpassword')
      } catch (error) {
        expect((error as Error).message).toBe(errorMessage)
      }

      expect(authStore.login).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword')
    })

    it('should handle logout', async () => {
      authStore.logout.mockResolvedValue(undefined)

      await authStore.logout()

      expect(authStore.logout).toHaveBeenCalled()
    })

    it('should handle user registration', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'New User',
      }

      authStore.register.mockResolvedValue(mockUser)

      const result = await authStore.register('<EMAIL>', 'password', 'New User')

      expect(authStore.register).toHaveBeenCalledWith('<EMAIL>', 'password', 'New User')
      expect(result).toEqual(mockUser)
    })
  })

  describe('Authentication Check', () => {
    it('should check authentication status', async () => {
      authStore.checkAuth.mockResolvedValue(true)

      const result = await authStore.checkAuth()

      expect(authStore.checkAuth).toHaveBeenCalled()
      expect(result).toBe(true)
    })
  })
})

describe('Visa Form Store', () => {
  let visaFormStore: ReturnType<typeof createMockVisaFormStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    visaFormStore = createMockVisaFormStore()
  })

  describe('State Management', () => {
    it('should initialize with default form data', () => {
      expect(visaFormStore.formData.value.personalInfo.surname).toBe('')
      expect(visaFormStore.formData.value.personalInfo.given_name).toBe('')
      expect(visaFormStore.formData.value.passportInfo.passport_number).toBe('')
      expect(visaFormStore.formData.value.contactInfo.email).toBe('')
      expect(visaFormStore.isDirty.value).toBe(false)
      expect(visaFormStore.isSubmitting.value).toBe(false)
    })
  })

  describe('Form Updates', () => {
    it('should update personal info', () => {
      const personalInfo = {
        surname: 'ZHANG',
        given_name: 'WEI',
        chinese_name: '张伟',
        sex: 'M',
        dob: '01/01/1990',
        place_of_birth: 'Beijing',
        nationality: 'CHN',
        religion: 'Buddhism',
      }

      visaFormStore.updatePersonalInfo(personalInfo)

      expect(visaFormStore.updatePersonalInfo).toHaveBeenCalledWith(personalInfo)
    })

    it('should update passport info', () => {
      const passportInfo = {
        passport_number: 'E12345678',
        date_of_issue: '01/01/2020',
        place_of_issue: 'Beijing',
        passport_expiry: '01/01/2030',
        passport_type: 'Ordinary passport',
      }

      visaFormStore.updatePassportInfo(passportInfo)

      expect(visaFormStore.updatePassportInfo).toHaveBeenCalledWith(passportInfo)
    })

    it('should update contact info', () => {
      const contactInfo = {
        email: '<EMAIL>',
        telephone_number: '+8613812345678',
        permanent_address: 'Beijing, China',
        contact_address: 'Beijing, China',
      }

      visaFormStore.updateContactInfo(contactInfo)

      expect(visaFormStore.updateContactInfo).toHaveBeenCalledWith(contactInfo)
    })

    it('should update visa info', () => {
      const visaInfo = {
        visa_entry_type: 'Single-entry',
        visa_validity_duration: '30天',
        visa_start_date: '01/06/2024',
        intended_entry_gate: 'Ho Chi Minh City',
        purpose_of_entry: 'Tourist',
      }

      visaFormStore.updateVisaInfo(visaInfo)

      expect(visaFormStore.updateVisaInfo).toHaveBeenCalledWith(visaInfo)
    })
  })

  describe('Form Actions', () => {
    it('should reset form', () => {
      visaFormStore.resetForm()

      expect(visaFormStore.resetForm).toHaveBeenCalled()
    })

    it('should validate form', () => {
      visaFormStore.validateForm.mockReturnValue({ isValid: true, errors: [] })

      const result = visaFormStore.validateForm()

      expect(visaFormStore.validateForm).toHaveBeenCalled()
      expect(result).toEqual({ isValid: true, errors: [] })
    })

    it('should save form', async () => {
      visaFormStore.saveForm.mockResolvedValue(true)

      const result = await visaFormStore.saveForm()

      expect(visaFormStore.saveForm).toHaveBeenCalled()
      expect(result).toBe(true)
    })

    it('should load form', async () => {
      const mockFormData = createTestFormData()
      visaFormStore.loadForm.mockResolvedValue(mockFormData)

      const result = await visaFormStore.loadForm()

      expect(visaFormStore.loadForm).toHaveBeenCalled()
      expect(result).toEqual(mockFormData)
    })
  })
})

describe('Submission Store', () => {
  let submissionStore: ReturnType<typeof createMockSubmissionStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    submissionStore = createMockSubmissionStore()
  })

  describe('State Management', () => {
    it('should initialize with default state', () => {
      expect(submissionStore.submissions.value).toEqual([])
      expect(submissionStore.currentSubmission.value).toBeNull()
      expect(submissionStore.isSubmitting.value).toBe(false)
      expect(submissionStore.error.value).toBeNull()
    })
  })

  describe('Submission Actions', () => {
    it('should submit application', async () => {
      const mockSubmission = {
        id: 'VN20240601ABC123',
        status: 'success',
        applicantName: 'ZHANG WEI',
        submitTime: new Date(),
      }

      submissionStore.submitApplication.mockResolvedValue(mockSubmission)

      const formData = createTestFormData()
      const result = await submissionStore.submitApplication(formData)

      expect(submissionStore.submitApplication).toHaveBeenCalledWith(formData)
      expect(result).toEqual(mockSubmission)
    })

    it('should get submissions', async () => {
      const mockSubmissions = [
        {
          id: 'VN20240601ABC123',
          status: 'success',
          applicantName: 'ZHANG WEI',
          submitTime: new Date(),
        },
        {
          id: 'VN20240602DEF456',
          status: 'pending',
          applicantName: 'LI MING',
          submitTime: new Date(),
        },
      ]

      submissionStore.getSubmissions.mockResolvedValue(mockSubmissions)

      const result = await submissionStore.getSubmissions()

      expect(submissionStore.getSubmissions).toHaveBeenCalled()
      expect(result).toEqual(mockSubmissions)
    })

    it('should get submission by id', async () => {
      const mockSubmission = {
        id: 'VN20240601ABC123',
        status: 'success',
        applicantName: 'ZHANG WEI',
        submitTime: new Date(),
      }

      submissionStore.getSubmissionById.mockResolvedValue(mockSubmission)

      const result = await submissionStore.getSubmissionById('VN20240601ABC123')

      expect(submissionStore.getSubmissionById).toHaveBeenCalledWith('VN20240601ABC123')
      expect(result).toEqual(mockSubmission)
    })

    it('should cancel submission', async () => {
      submissionStore.cancelSubmission.mockResolvedValue(true)

      const result = await submissionStore.cancelSubmission('VN20240601ABC123')

      expect(submissionStore.cancelSubmission).toHaveBeenCalledWith('VN20240601ABC123')
      expect(result).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle submission errors', async () => {
      const errorMessage = 'Submission failed'
      submissionStore.submitApplication.mockRejectedValue(new Error(errorMessage))

      try {
        await submissionStore.submitApplication(createTestFormData())
      } catch (error) {
        expect((error as Error).message).toBe(errorMessage)
      }
    })

    it('should clear errors', () => {
      submissionStore.clearError()

      expect(submissionStore.clearError).toHaveBeenCalled()
    })
  })
})

describe('Notification Store', () => {
  let notificationStore: ReturnType<typeof createMockNotificationStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    notificationStore = createMockNotificationStore()
  })

  describe('State Management', () => {
    it('should initialize with default state', () => {
      expect(notificationStore.notifications.value).toEqual([])
      expect(notificationStore.unreadCount.value).toBe(0)
    })
  })

  describe('Notification Actions', () => {
    it('should add notification', () => {
      const notification = {
        id: '1',
        type: 'success',
        title: 'Success',
        message: 'Operation completed successfully',
        timestamp: new Date(),
      }

      notificationStore.addNotification(notification)

      expect(notificationStore.addNotification).toHaveBeenCalledWith(notification)
    })

    it('should mark notification as read', () => {
      notificationStore.markAsRead('1')

      expect(notificationStore.markAsRead).toHaveBeenCalledWith('1')
    })

    it('should clear all notifications', () => {
      notificationStore.clearNotifications()

      expect(notificationStore.clearNotifications).toHaveBeenCalled()
    })

    it('should show success notification', () => {
      notificationStore.showSuccess('Success message')

      expect(notificationStore.showSuccess).toHaveBeenCalledWith('Success message')
    })

    it('should show error notification', () => {
      notificationStore.showError('Error message')

      expect(notificationStore.showError).toHaveBeenCalledWith('Error message')
    })

    it('should show warning notification', () => {
      notificationStore.showWarning('Warning message')

      expect(notificationStore.showWarning).toHaveBeenCalledWith('Warning message')
    })

    it('should show info notification', () => {
      notificationStore.showInfo('Info message')

      expect(notificationStore.showInfo).toHaveBeenCalledWith('Info message')
    })
  })
})
