"""
Logger Configuration Module

Configures and initializes the application logging system.
"""

from datetime import datetime
import logging
from pathlib import Path
import sys
from zoneinfo import ZoneInfo

from loguru import logger


def setup_logger(
    log_dir: str = "logs",
    console_level: str = "INFO",
    file_level: str = "DEBUG",
    log_filename_prefix: str = "vietnam_evisa_app",
    user_display_timezone: str
    | None = None,  # 可选参数，如果不提供则使用settings中的配置
) -> None:
    """
    Set up the application logger.

    Configures the logger to output to both console and log files.

    Args:
        log_dir: Directory to store log files
        console_level: Log level for console output
        file_level: Log level for file output
        log_filename_prefix: Prefix for log file names
        user_display_timezone: Optional timezone for user-friendly display (uses settings if not provided)
    """
    # 导入settings配置
    try:
        from backend.config.settings import get_log_display_timezone

        display_timezone = user_display_timezone or get_log_display_timezone()
    except ImportError:
        display_timezone = user_display_timezone or "Asia/Shanghai"

    # Create log directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)

    # 系统时区（UTC）和用户显示时区
    utc_tz = ZoneInfo("UTC")
    display_tz = ZoneInfo(display_timezone)

    # Generate log filename with timestamp (使用UTC时间)
    timestamp = datetime.now(utc_tz).strftime("%Y%m%d_%H%M%S")
    log_filename = log_path / f"{log_filename_prefix}_{timestamp}.log"

    # Remove default handler
    logger.remove()

    # 时区过滤器函数 - 内部使用UTC，显示使用用户时区
    def timezone_filter_for_console(record):
        """
        Console display: Convert UTC time to user display timezone
        用户友好的控制台显示 - 转换为北京时间
        """
        if record["time"].tzinfo is None:
            record["time"] = record["time"].replace(tzinfo=utc_tz)
        else:
            record["time"] = record["time"].astimezone(utc_tz)

        # 为控制台显示转换为用户时区
        record["time"] = record["time"].astimezone(display_tz)
        return True

    def timezone_filter_for_file(record):
        """
        File storage: Keep UTC for system consistency
        文件存储 - 保持UTC以确保系统一致性
        """
        if record["time"].tzinfo is None:
            record["time"] = record["time"].replace(tzinfo=utc_tz)
        else:
            record["time"] = record["time"].astimezone(utc_tz)
        return True

    # Add console handler (显示用户友好的北京时间)
    logger.add(
        sys.stderr,
        level=console_level.upper(),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        filter=timezone_filter_for_console,
    )

    # Add file handler (存储UTC时间保证系统一致性)
    logger.add(
        str(log_filename),
        level=file_level.upper(),
        format="{time:YYYY-MM-DD HH:mm:ss UTC} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="1 week",
        compression="zip",
        filter=timezone_filter_for_file,
    )

    logger.info(f"Logger initialized. Log file: {log_filename}")
    logger.info(f"Console display timezone: {display_timezone}")
    logger.info("File storage timezone: UTC")


def get_logger():
    """
    Get the configured logger.

    Returns:
        Configured logger instance
    """
    return logger


def setup_logging(
    log_level: int = logging.INFO,
    enable_file_logging: bool = True,
    log_rotation: bool = True,
    log_compression: bool = True,
) -> None:
    """
    设置全局日志配置

    Args:
        log_level: 日志级别
        enable_file_logging: 是否启用文件日志
        log_rotation: 是否启用日志轮转
        log_compression: 是否启用日志压缩
    """
    # ... existing code ...


# 明确导出所有需要的函数和变量
__all__ = [
    "setup_logger",
    "get_logger",
    "setup_logging",
    "logger",
]
