import { request } from '@/api/request'
import type { VisaApplicationApiResponse } from '@/api/types'
import type { ApplicationItem } from '@/stores/application'
import { useApplicationStore } from '@/stores/application'
import { useNotificationStore } from '@/stores/notification'
import { useSubmissionStore } from '@/stores/submission'
import type { VisaFormData } from '@/types/form'
import { redirectToLogin } from '@/utils/navigation'
import { formatDateTime, getCurrentUTCTimestamp } from '@/utils/timeFormat'
import { ElMessageBox } from 'element-plus'
import { ref } from 'vue'

// 提交状态接口
export interface SubmissionStatus {
  isSubmitting: boolean
  buttonText: string
  disabled: boolean
}

// Helper function to check if error is an authentication error
function isAuthError(error: unknown): boolean {
  if (!error || typeof error !== 'object') {
    return false
  }

  // Check for HTTP 401 error
  if ('response' in error) {
    const httpError = error as { response?: { status?: number } }
    if (httpError.response?.status === 401) {
      return true
    }
  }

  // Check for session expired message
  if ('message' in error) {
    const messageError = error as { message?: string }
    if (typeof messageError.message === 'string' && messageError.message.includes('会话已过期')) {
      return true
    }
  }

  return false
}

export const useSubmission = () => {
  const submissionStore = useSubmissionStore()
  const applicationStore = useApplicationStore()
  const notificationStore = useNotificationStore()

  // 提交状态响应式数据
  const submissionStatus = ref<SubmissionStatus>({
    isSubmitting: false,
    buttonText: '提交申请',
    disabled: false,
  })

  // 显示确认对话框，严格对应旧版Utils.showConfirm
  const showConfirmDialog = async (
    title: string,
    message: string,
    confirmText = '确认',
    cancelText = '取消',
  ): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        type: 'warning',
        dangerouslyUseHTMLString: true,
      })
      return true
    } catch {
      return false
    }
  }

  // 处理本地重复提交检查，优化为快速重复点击检查
  const handleQuickDuplicateCheck = async (passportNumber: string): Promise<boolean> => {
    if (submissionStore.isQuickDuplicateSubmission(passportNumber)) {
      const userConfirm = await showConfirmDialog(
        '快速重复提交确认',
        `护照号 ${passportNumber} 刚刚已经提交过申请（1分钟内）。<br><br>是否确认重新提交？<br><br><strong>注意：快速重复提交可能是误操作。</strong>`,
        '确认提交',
        '取消',
      )

      return userConfirm
    }
    return true
  }

  // 处理服务器端重复提交检查，严格对应旧版逻辑
  const handleServerDuplicateCheck = async (
    passportNumber: string,
    formData: FormData,
  ): Promise<boolean> => {
    try {
      const checkResult = await submissionStore.checkServerDuplicate(passportNumber)

      if (checkResult.exists) {
        // 根据can_resubmit决定是否需要弹窗
        if (checkResult.can_resubmit) {
          // 可以直接重新提交，无需弹窗
          return true
        } else {
          // 需要弹窗提醒用户

          let message = ''
          const title = '重复提交确认'

          // 🔥 优化：更好地处理订单系统的数据
          const displayId = checkResult.order_no ?? checkResult.application_number ?? '未知'
          const isOrderNo = checkResult.order_no && checkResult.order_no.startsWith('VN')
          const idType = isOrderNo ? '订单编号' : '申请编号'

          switch (checkResult.warning_type) {
            case 'success':
              message = `护照号 ${passportNumber} 之前已成功提交过申请。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br>状态：提交成功<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
              break
            case 'pending':
              message = `护照号 ${passportNumber} 的申请正在提交中。<br><br>${idType}：${displayId}<br>当前状态：${checkResult.status ?? '处理中'}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
              break
            case 'failed':
              // 失败状态通常会直接重新提交，但如果到这里说明有特殊情况
              message = `护照号 ${passportNumber} 之前的申请处理失败。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？`
              break
            default:
              message = `护照号 ${passportNumber} 之前已提交过申请。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
          }

          const userConfirm = await showConfirmDialog(title, message, '确认重新提交', '取消')

          if (!userConfirm) {
            return false
          } else {
            // 继续提交，但需要告诉后端这是用户确认的重复提交
            formData.append('force_resubmit', 'true')
            return true
          }
        }
      }
      return true
    } catch (error) {
      if (error instanceof Error && error.message === 'UNAUTHORIZED') {
        // 会话过期，显示错误并重定向
        notificationStore.showAuthError('您的登录会话已过期，请重新登录。')
        setTimeout(() => {
          redirectToLogin()
        }, 1500)
        return false
      } else {
        // 🔥 关键修复：如果重复检查失败，应该继续提交流程，而不是阻止提交
        return true
      }
    }
  }

  // 更新提交按钮状态
  const updateSubmissionButton = (isSubmitting: boolean, originalText: string = '提交申请') => {
    submissionStatus.value = {
      isSubmitting,
      buttonText: isSubmitting ? '提交中...' : originalText,
      disabled: isSubmitting,
    }
  }

  // 构建表单数据，严格对应旧版FormData构建逻辑
  const buildFormData = (formData: VisaFormData): FormData => {
    const submitFormData = new FormData()

    // 遍历所有字段并添加到FormData
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (value instanceof File) {
          submitFormData.append(key, value)
        } else {
          submitFormData.append(key, String(value))
        }
      }
    })

    return submitFormData
  }

  // 🔥 删除：无用的监控函数，状态更新由轮询机制处理

  // 主提交方法，严格迁移旧版完整流程
  const submitApplication = async (formData: VisaFormData): Promise<boolean> => {
    // 检查是否在登录页面，如果是则中止提交
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      submissionStore.resetSubmissionState()
      return false
    }

    try {
      // 🔧 关键修复：开始提交前强制确保状态是干净的
      if (submissionStore.isSubmitting) {
        submissionStore.resetSubmissionState()
      }

      // 🟢 基于状态的防重复点击检查
      if (submissionStore.isSubmitting) {
        notificationStore.showWarning('申请正在提交中，请勿重复点击提交按钮。')
        return false
      }

      // 🟢 立即更新按钮状态，提供视觉反馈
      updateSubmissionButton(true)

      // 🔧 移除重复的会话检查，让API调用自然触发错误处理
      // 这样可以确保SESSION_INVALIDATED错误能被正确处理和显示

      // 开始提交流程
      submissionStore.startSubmission(formData.passport_number)

      // 检查本地重复提交
      const localCheckPassed = await handleQuickDuplicateCheck(formData.passport_number)
      if (!localCheckPassed) {
        submissionStore.resetSubmissionState()
        updateSubmissionButton(false)
        return false
      }

      // 构建FormData
      const submitFormData = buildFormData(formData)

      // 检查服务器端重复提交
      const serverCheckPassed = await handleServerDuplicateCheck(
        formData.passport_number,
        submitFormData,
      )
      if (!serverCheckPassed) {
        submissionStore.resetSubmissionState()
        updateSubmissionButton(false)
        return false
      }

      // 显示加载状态
      notificationStore.showInfo('正在提交申请，请稍候...')

      // 🔧 修复：同步获取orderNo，异步处理自动化任务
      try {
        // 🔧 GitHub Copilot建议A：使用精确类型而非any转换
        const response = await request.upload<VisaApplicationApiResponse>(
          '/api/visa/apply',
          submitFormData,
        )

        // 🔧 修复：API拦截器已返回data部分，直接访问tracking_info
        const orderNo = response?.tracking_info?.order_no

        // 🔥 简化：只设置必要字段，其他字段会自动为undefined
        const applicationData: ApplicationItem = {
          passportNumber: formData.passport_number,
          applicantName:
            `${formData.surname} ${formData.given_name}`.trim() ??
            formData.chinese_name ??
            formData.passport_number,
          chineseName: formData.chinese_name,
          submissionTime: formatDateTime(getCurrentUTCTimestamp()),
          orderStatus: 'created',
          orderNo: orderNo,
          automationStatus: 'processing',
        }

        applicationStore.addApplication(applicationData)

        notificationStore.showOperationSuccess(
          `申请已提交！\n\n订单编号: ${orderNo}\n\n护照号: ${formData.passport_number}\n\n申请正在后台处理中，请稍后查看结果。`,
        )

        submissionStore.markSubmissionSuccess(formData.passport_number)

        // 🔥 删除：状态监控由轮询机制统一处理，无需单独启动

        // 重置按钮状态
        updateSubmissionButton(false)
      } catch (error: unknown) {
        // 🔧 修复：正确处理API错误响应
        console.error('❌ API调用失败:', error)

        // 提取错误信息
        let errorMessage = '提交失败，请重试'

        if (error && typeof error === 'object') {
          // 检查是否是API错误响应
          if ('response' in error && error.response && typeof error.response === 'object') {
            const response = error.response as { data?: { message?: string; detail?: string } }
            errorMessage = response.data?.message || response.data?.detail || errorMessage
          } else if (
            'message' in error &&
            typeof (error as { message?: string }).message === 'string'
          ) {
            errorMessage = (error as { message: string }).message
          }
        }

        notificationStore.showError(errorMessage)
        submissionStore.markSubmissionFailed(errorMessage)
        updateSubmissionButton(false)
        return false
      }

      return true
    } catch (error: unknown) {
      // 🔥 修复：不要在这里处理认证错误，让Axios拦截器统一处理
      // 认证错误（包括SESSION_INVALIDATED）应该由API拦截器处理

      // 检查是否是认证错误
      if (isAuthError(error)) {
        // 不显示任何消息，让Axios拦截器处理
        return false
      }

      // 🔥 修复：安全地获取错误消息
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'message' in error &&
        typeof (error as { message?: string }).message === 'string'
          ? (error as { message: string }).message
          : '提交失败，请重试'

      submissionStore.markSubmissionFailed(errorMessage)
      updateSubmissionButton(false)
      return false
    } finally {
      // 🔧 新增：确保按钮状态在异常情况下也能正确重置
      // 延迟重置，确保正常流程中的按钮状态不被覆盖
      setTimeout(() => {
        if (submissionStore.isSubmitting) {
          updateSubmissionButton(false)
        }
      }, 1000)
    }
  }

  return {
    // 状态
    submissionStatus,

    // 方法
    submitApplication,
    updateSubmissionButton,
  }
}
