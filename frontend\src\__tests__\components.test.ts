/**
 * Vue组件单元测试
 * ===============
 *
 * 测试核心Vue组件的渲染和交互
 */

import userEvent from '@testing-library/user-event'
import { fireEvent, screen } from '@testing-library/vue'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createTestFormData, renderComponent } from './test-utils'

// Mock components since we're testing the logic, not the full components
const createMockVisaPersonalInfoForm = () => ({
  name: 'MockVisaPersonalInfoForm',
  template: `
    <form data-testid="personal-info-form">
      <div>
        <label for="surname">姓氏</label>
        <input
          id="surname"
          v-model="personalInfo.surname"
          data-testid="surname-input"
          placeholder="护照上的姓氏"
        />
      </div>
      <div>
        <label for="given-name">名字</label>
        <input
          id="given-name"
          v-model="personalInfo.given_name"
          data-testid="given-name-input"
          placeholder="护照上的名字"
        />
      </div>
      <div>
        <label for="sex">性别</label>
        <div>
          <input
            id="sex-male"
            type="radio"
            v-model="personalInfo.sex"
            value="M"
            data-testid="sex-male"
          />
          <label for="sex-male">男</label>
          <input
            id="sex-female"
            type="radio"
            v-model="personalInfo.sex"
            value="F"
            data-testid="sex-female"
          />
          <label for="sex-female">女</label>
        </div>
      </div>
      <div>
        <label for="email">邮箱</label>
        <input
          id="email"
          v-model="contactInfo.email"
          data-testid="email-input"
          placeholder="邮箱地址"
        />
      </div>
      <div>
        <label for="passport-number">护照号码</label>
        <input
          id="passport-number"
          v-model="passportInfo.passport_number"
          data-testid="passport-number-input"
          placeholder="护照号码"
        />
      </div>
    </form>
  `,
  props: {
    personalInfo: {
      type: Object,
      required: true,
    },
    passportInfo: {
      type: Object,
      required: true,
    },
    contactInfo: {
      type: Object,
      required: true,
    },
  },
})

const createMockFileUploadPanel = () => ({
  name: 'MockFileUploadPanel',
  template: `
    <div data-testid="file-upload-panel">
      <div>
        <label for="passport-upload">护照扫描件</label>
        <input
          id="passport-upload"
          type="file"
          accept="image/*"
          data-testid="passport-upload"
          @change="handleFileChange"
        />
      </div>
      <div>
        <label for="portrait-upload">证件照</label>
        <input
          id="portrait-upload"
          type="file"
          accept="image/*"
          data-testid="portrait-upload"
          @change="handleFileChange"
        />
      </div>
      <div v-if="isProcessing" data-testid="processing-indicator">
        处理中...
      </div>
    </div>
  `,
  props: {
    isProcessing: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['file-change'],
  methods: {
    handleFileChange(event: Event) {
      ;(this as unknown as { $emit: (eventName: string, event: Event) => void }).$emit(
        'file-change',
        event,
      )
    },
  },
})

describe('VisaPersonalInfoForm', () => {
  const defaultProps = {
    personalInfo: {
      surname: '',
      given_name: '',
      chinese_name: '',
      sex: '',
      dob: '',
      place_of_birth: '',
      nationality: '',
      religion: '',
    },
    passportInfo: {
      passport_number: '',
      date_of_issue: '',
      place_of_issue: '',
      passport_expiry: '',
      passport_type: 'Ordinary passport',
    },
    contactInfo: {
      email: '',
      telephone_number: '',
      permanent_address: '',
      contact_address: '',
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render personal information form fields', () => {
    renderComponent(createMockVisaPersonalInfoForm(), {
      props: defaultProps,
    })

    expect(screen.getByTestId('personal-info-form')).toBeInTheDocument()
    expect(screen.getByTestId('surname-input')).toBeInTheDocument()
    expect(screen.getByTestId('given-name-input')).toBeInTheDocument()
    expect(screen.getByTestId('sex-male')).toBeInTheDocument()
    expect(screen.getByTestId('sex-female')).toBeInTheDocument()
    expect(screen.getByTestId('email-input')).toBeInTheDocument()
    expect(screen.getByTestId('passport-number-input')).toBeInTheDocument()
  })

  it('should display form field labels correctly', () => {
    renderComponent(createMockVisaPersonalInfoForm(), {
      props: defaultProps,
    })

    expect(screen.getByLabelText('姓氏')).toBeInTheDocument()
    expect(screen.getByLabelText('名字')).toBeInTheDocument()
    // 性别字段有两个单选按钮，分别测试它们的标签
    expect(screen.getByLabelText('男')).toBeInTheDocument()
    expect(screen.getByLabelText('女')).toBeInTheDocument()
    expect(screen.getByLabelText('邮箱')).toBeInTheDocument()
    expect(screen.getByLabelText('护照号码')).toBeInTheDocument()
  })

  it('should handle user input correctly', async () => {
    const user = userEvent.setup()
    const testData = createTestFormData()

    renderComponent(createMockVisaPersonalInfoForm(), {
      props: {
        ...defaultProps,
        personalInfo: { ...defaultProps.personalInfo, ...testData },
        passportInfo: { ...defaultProps.passportInfo, ...testData },
        contactInfo: { ...defaultProps.contactInfo, ...testData },
      },
    })

    const surnameInput = screen.getByTestId('surname-input')
    const givenNameInput = screen.getByTestId('given-name-input')
    const emailInput = screen.getByTestId('email-input')

    await user.clear(surnameInput)
    await user.type(surnameInput, 'ZHANG')

    await user.clear(givenNameInput)
    await user.type(givenNameInput, 'WEI')

    await user.clear(emailInput)
    await user.type(emailInput, '<EMAIL>')

    expect(surnameInput).toHaveValue('ZHANG')
    expect(givenNameInput).toHaveValue('WEI')
    expect(emailInput).toHaveValue('<EMAIL>')
  })

  it('should handle radio button selection', async () => {
    const user = userEvent.setup()

    renderComponent(createMockVisaPersonalInfoForm(), {
      props: defaultProps,
    })

    const maleRadio = screen.getByTestId('sex-male')
    const femaleRadio = screen.getByTestId('sex-female')

    await user.click(maleRadio)
    expect(maleRadio).toBeChecked()
    expect(femaleRadio).not.toBeChecked()

    await user.click(femaleRadio)
    expect(femaleRadio).toBeChecked()
    expect(maleRadio).not.toBeChecked()
  })

  it('should show placeholder text for inputs', () => {
    renderComponent(createMockVisaPersonalInfoForm(), {
      props: defaultProps,
    })

    expect(screen.getByPlaceholderText('护照上的姓氏')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('护照上的名字')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('邮箱地址')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('护照号码')).toBeInTheDocument()
  })
})

describe('FileUploadPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render file upload inputs', () => {
    renderComponent(createMockFileUploadPanel())

    expect(screen.getByTestId('file-upload-panel')).toBeInTheDocument()
    expect(screen.getByTestId('passport-upload')).toBeInTheDocument()
    expect(screen.getByTestId('portrait-upload')).toBeInTheDocument()
  })

  it('should show processing indicator when uploading', () => {
    renderComponent(createMockFileUploadPanel(), {
      props: { isProcessing: true },
    })

    expect(screen.getByTestId('processing-indicator')).toBeInTheDocument()
    expect(screen.getByText('处理中...')).toBeInTheDocument()
  })

  it('should not show processing indicator when not uploading', () => {
    renderComponent(createMockFileUploadPanel(), {
      props: { isProcessing: false },
    })

    expect(screen.queryByTestId('processing-indicator')).not.toBeInTheDocument()
  })

  it('should handle file selection', async () => {
    const mockFileChangeHandler = vi.fn()

    renderComponent(createMockFileUploadPanel(), {
      props: { isProcessing: false },
      attrs: {
        onFileChange: mockFileChangeHandler,
      },
    })

    const file = new File(['passport'], 'passport.jpg', { type: 'image/jpeg' })
    const fileInput = screen.getByTestId('passport-upload')

    await fireEvent.change(fileInput, { target: { files: [file] } })

    expect(mockFileChangeHandler).toHaveBeenCalled()
  })

  it('should have correct file input attributes', () => {
    renderComponent(createMockFileUploadPanel())

    const passportUpload = screen.getByTestId('passport-upload')
    const portraitUpload = screen.getByTestId('portrait-upload')

    expect(passportUpload).toHaveAttribute('type', 'file')
    expect(passportUpload).toHaveAttribute('accept', 'image/*')
    expect(portraitUpload).toHaveAttribute('type', 'file')
    expect(portraitUpload).toHaveAttribute('accept', 'image/*')
  })
})

describe('Form Validation Integration', () => {
  it('should validate required fields', async () => {
    const mockValidation = {
      validateField: vi.fn(),
      hasError: vi.fn(),
      getError: vi.fn(),
    }

    // Mock validation behavior
    mockValidation.validateField.mockImplementation((field: string, value: string) => {
      if (!value) {
        mockValidation.hasError.mockReturnValue(true)
        mockValidation.getError.mockReturnValue('此字段为必填项')
        return false
      }
      mockValidation.hasError.mockReturnValue(false)
      mockValidation.getError.mockReturnValue('')
      return true
    })

    expect(mockValidation.validateField('surname', '')).toBe(false)
    expect(mockValidation.hasError()).toBe(true)
    expect(mockValidation.getError()).toBe('此字段为必填项')

    expect(mockValidation.validateField('surname', 'ZHANG')).toBe(true)
    expect(mockValidation.hasError()).toBe(false)
    expect(mockValidation.getError()).toBe('')
  })

  it('should validate email format', () => {
    const validateEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email)
    }

    expect(validateEmail('<EMAIL>')).toBe(true)
    expect(validateEmail('invalid-email')).toBe(false)
    expect(validateEmail('test@')).toBe(false)
    expect(validateEmail('@example.com')).toBe(false)
  })

  it('should validate passport number format', () => {
    const validatePassportNumber = (passport: string) => {
      const passportRegex = /^[A-Za-z0-9]{6,12}$/
      return passportRegex.test(passport.replace(/\s/g, ''))
    }

    expect(validatePassportNumber('E12345678')).toBe(true)
    expect(validatePassportNumber('AB123456')).toBe(true)
    expect(validatePassportNumber('12345')).toBe(false)
    expect(validatePassportNumber('ABCDEFGHIJKLMNOP')).toBe(false)
  })
})
