"""
基本测试模块
===========

验证pytest测试发现机制是否正常工作
"""

import pytest


class TestBasicFunctionality:
    """基础功能测试"""

    def test_simple_assertion(self):
        """测试简单断言"""
        assert 1 + 1 == 2

    def test_string_operations(self):
        """测试字符串操作"""
        test_string = "visa-automator"
        assert "visa" in test_string
        assert test_string.startswith("visa")
        assert test_string.endswith("automator")

    def test_list_operations(self):
        """测试列表操作"""
        test_list = [1, 2, 3, 4, 5]
        assert len(test_list) == 5
        assert 3 in test_list
        assert test_list[0] == 1

    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """测试异步功能"""
        import asyncio

        async def sample_async_func():
            await asyncio.sleep(0.01)
            return "async_result"

        result = await sample_async_func()
        assert result == "async_result"


def test_module_level_function():
    """模块级别的测试函数"""
    assert True is True
    assert False is False
