/**
 * Composables单元测试
 * ==================
 *
 * 测试组合式函数的业务逻辑
 */

import { useFormValidation } from '@/composables/useFormValidation'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock API
vi.mock('@/api/request', () => ({
  api: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}))

describe('useFormValidation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Field Validation', () => {
    it('should validate email format correctly', () => {
      const { isValidEmail } = useFormValidation()

      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@domain.com')).toBe(false)
    })

    it('should validate phone number format correctly', () => {
      const { isValidPhone } = useFormValidation()

      expect(isValidPhone('+84*********')).toBe(true)
      expect(isValidPhone('0*********')).toBe(true)
      expect(isValidPhone('+****************')).toBe(true)
      expect(isValidPhone('12345')).toBe(false)
      expect(isValidPhone('abc123')).toBe(false)
    })

    it('should validate passport number format correctly', () => {
      const { isValidPassportNumber } = useFormValidation()

      expect(isValidPassportNumber('E12345678')).toBe(true)
      expect(isValidPassportNumber('AB123456')).toBe(true)
      expect(isValidPassportNumber('*********')).toBe(true)
      expect(isValidPassportNumber('12345')).toBe(false)
      expect(isValidPassportNumber('ABCDEFGHIJK123')).toBe(false)
    })

    it('should validate date format DD/MM/YYYY correctly', () => {
      const { isValidDate } = useFormValidation()

      expect(isValidDate('15/06/2024')).toBe(true)
      expect(isValidDate('1/1/2024')).toBe(true)
      expect(isValidDate('31/12/2023')).toBe(true)
      expect(isValidDate('32/12/2023')).toBe(false)
      expect(isValidDate('15/13/2023')).toBe(false)
      expect(isValidDate('15-06-2024')).toBe(false)
      expect(isValidDate('2024/06/15')).toBe(false)
    })

    it('should disable past dates correctly', () => {
      const { disabledDate } = useFormValidation()

      const today = new Date()
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

      expect(disabledDate(yesterday)).toBe(true)
      expect(disabledDate(tomorrow)).toBe(false)
    })
  })

  describe('Field Error Management', () => {
    it('should manage field errors correctly', () => {
      const { setFieldError, getFieldError, hasFieldError, clearFieldError } = useFormValidation()

      // Initially no errors
      expect(hasFieldError('email')).toBe(false)
      expect(getFieldError('email')).toBe('')

      // Set error
      setFieldError('email', 'Invalid email format')
      expect(hasFieldError('email')).toBe(true)
      expect(getFieldError('email')).toBe('Invalid email format')

      // Clear error
      clearFieldError('email')
      expect(hasFieldError('email')).toBe(false)
      expect(getFieldError('email')).toBe('')
    })
  })

  describe('Form Validation', () => {
    it('should validate required fields', () => {
      const { validateFields } = useFormValidation()

      const data = {
        name: 'John',
        email: '',
        phone: '*********',
      }

      const result = validateFields(data, ['name', 'email', 'phone'])

      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveProperty('email')
      expect(result.errors.email).toBe('此字段为必填项')
    })

    it('should validate visa info completely', () => {
      const { validateVisaInfo } = useFormValidation()

      const incompleteVisaInfo = {
        visa_entry_type: '' as const,
        visa_validity_duration: '' as const,
        visa_start_date: '',
        intended_entry_gate: '',
        purpose_of_entry: 'Tourist' as const,
        has_vietnam_contact: false,
        vietnam_contact_organization: '',
        vietnam_contact_phone: '',
        vietnam_contact_address: '',
      }

      const result = validateVisaInfo(incompleteVisaInfo)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('请选择签证类型')
      expect(result.errors).toContain('请选择有效期')
      expect(result.errors).toContain('请选择出签生效日期')
      expect(result.errors).toContain('请选择预期入境口岸')
    })
  })

  describe('Real-time Validation', () => {
    it('should validate field in real-time', () => {
      const { validateFieldRealtime, getFieldError } = useFormValidation()

      // Valid field
      expect(validateFieldRealtime('name', 'John')).toBe(true)
      expect(getFieldError('name')).toBe('')

      // Invalid field
      expect(validateFieldRealtime('email', '')).toBe(false)
      expect(getFieldError('email')).toBe('此字段为必填项')
    })
  })
})
