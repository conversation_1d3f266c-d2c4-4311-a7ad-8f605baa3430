"""
增强的Repository层测试 - 修复版本
================================

专注于测试Repository层实际存在的功能
使用真实数据库操作，确保测试的有效性和价值
"""

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestOrderRepositoryEnhanced:
    """增强的订单Repository测试 - 修复版本"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        """创建OrderRepository实例"""
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """创建测试用户"""
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_order_status_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单状态查询功能"""
        # 创建不同状态的订单 - 只使用实际存在的状态
        statuses = [OrderStatus.CREATED, OrderStatus.CANCELLED]
        created_orders = []

        for status in statuses:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": status.value,
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试按状态查询 - 使用实际存在的方法
        created_orders_result = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders_result) >= 1
        assert all(
            order.order_status == OrderStatus.CREATED.value
            for order in created_orders_result
        )

        cancelled_orders_result = await order_repo.get_orders_by_status(
            OrderStatus.CANCELLED, user_id=test_user.id
        )
        assert len(cancelled_orders_result) >= 1

    @pytest.mark.asyncio
    async def test_order_search_by_order_no(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试按订单号搜索功能"""
        # 创建测试订单
        order_data = {
            "user_id": test_user.id,
            "order_status": OrderStatus.CREATED.value,
        }
        order = TestDataFactory.create_order_data(order_data)
        created_order = await order_repo.create(order)

        # 测试按订单号查询
        found_order = await order_repo.get_by_order_no(
            created_order.order_no, user_id=test_user.id
        )
        assert found_order is not None
        assert found_order.id == created_order.id

        # 测试查询不存在的订单号
        not_found = await order_repo.get_by_order_no(
            "NONEXISTENT", user_id=test_user.id
        )
        assert not_found is None

    @pytest.mark.asyncio
    async def test_order_basic_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试基本的订单查询功能"""
        # 创建订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": OrderStatus.CREATED.value,
            }
        )
        created_order = await order_repo.create(order)

        # 测试基本查询
        found_order = await order_repo.get_by_id(created_order.id)
        assert found_order is not None
        assert found_order.order_status == OrderStatus.CREATED.value

    @pytest.mark.asyncio
    async def test_order_pagination(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试分页查询功能"""
        # 创建多个订单
        total_orders = 15
        for i in range(total_orders):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711TEST{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试第一页
        page1 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=0)
        assert len(page1) == 10

        # 测试第二页
        page2 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=10)
        assert len(page2) == 5  # 剩余5个

    @pytest.mark.asyncio
    async def test_order_constraint_violations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试数据库约束违反的处理"""
        # 创建订单
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",
            }
        )
        await order_repo.create(order1)

        # 尝试创建重复订单号的订单
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",  # 重复的订单号
            }
        )

        # 应该抛出完整性错误
        with pytest.raises(IntegrityError):
            await order_repo.create(order2)

    @pytest.mark.asyncio
    async def test_order_duplicate_detection(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试重复订单检测功能"""
        # 创建订单
        idempotent_key = "test_key_12345"
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "idempotent_key": idempotent_key,
            }
        )
        created_order = await order_repo.create(order)

        # 测试重复检测
        duplicate = await order_repo.check_duplicate_order(
            test_user.id, idempotent_key, within_seconds=60
        )
        assert duplicate is not None
        assert duplicate.id == created_order.id

    @pytest.mark.asyncio
    async def test_order_crud_operations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试基本的CRUD操作"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)
        assert created_order.id is not None

        # 获取实际的 UUID 值
        order_id = created_order.id

        # 读取订单
        found_order = await order_repo.get_by_id(order_id)
        assert found_order is not None

        # 更新订单
        found_order.notes = "Updated notes"
        updated_order = await order_repo.update(found_order)
        assert updated_order.notes == "Updated notes"

        # 删除订单
        delete_result = await order_repo.delete(order_id)
        assert delete_result is True

        # 验证删除
        deleted_order = await order_repo.get_by_id(order_id)
        assert deleted_order is None
