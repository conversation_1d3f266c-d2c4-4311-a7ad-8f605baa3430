# api/routes/models.py
from typing import Any

from pydantic import BaseModel, field_validator


class VisaFormTextFields(BaseModel):
    """签证表单文本字段"""

    surname: str
    given_name: str
    chinese_name: str | None = None
    sex: str
    dob: str
    place_of_birth: str
    nationality: str = "CHINA"
    religion: str = "NO"
    passport_number: str
    passport_type: str = "Ordinary passport"
    place_of_issue: str
    date_of_issue: str
    passport_expiry: str
    email: str
    telephone_number: str
    permanent_address: str | None = None
    contact_address: str | None = None
    emergency_contact_name: str | None = None
    emergency_address: str | None = None
    emergency_contact_phone: str | None = None
    visa_entry_type: str
    visa_validity_duration: str
    visa_start_date: str
    intended_entry_gate: str

    @field_validator("*", mode="before")
    @classmethod
    def empty_str_as_none(cls, v: Any):
        if isinstance(v, str) and v.strip() == "":
            return None
        return v


class VisaApplicationResponse(BaseModel):
    """签证申请响应"""

    success: bool
    message: str
    application_id: str | None = None
    redirect_url: str | None = None


class OCRResponse(BaseModel):
    """OCR识别响应"""

    success: bool
    message: str
    fields: dict | None = None
