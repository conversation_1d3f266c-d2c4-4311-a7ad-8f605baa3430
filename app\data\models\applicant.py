import uuid

from sqlalchemy import (
    CheckConstraint,
    Column,
    Date,
    DateTime,
    ForeignKey,
    Index,
    String,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import validates
from sqlalchemy.sql import func

from ..base import Base


class Applicant(Base):
    __tablename__ = "applicant"

    # 主键
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="申请人主键"
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        doc="所属用户",
    )

    # 基本身份信息（不变的信息）
    surname = Column(String(64), nullable=False, doc="姓")
    given_name = Column(String(64), nullable=False, doc="名")
    chinese_name = Column(String(64), doc="中文名")
    sex = Column(String(8), doc="性别")
    nationality = Column(String(32), doc="国籍")
    date_of_birth = Column(Date, doc="出生日期")
    place_of_birth = Column(String(128), doc="出生地")

    # 护照信息（相对稳定的信息）
    passport_number = Column(String(32), nullable=False, doc="护照号")
    passport_type = Column(String(32), doc="护照类型")
    date_of_issue = Column(Date, doc="签发日期")
    date_of_expiry = Column(Date, doc="到期日期")
    place_of_issue = Column(String(128), doc="签发地")

    # 联系方式（可能变化的信息）
    telephone_number = Column(String(32), doc="联系电话")
    email = Column(String(128), doc="邮箱")
    permanent_address = Column(String(256), doc="永久地址")
    contact_address = Column(String(256), doc="联系地址")

    # 工作信息（相对稳定）
    work_unit = Column(String(128), doc="工作单位")
    work_address = Column(String(256), doc="工作地址")

    # 紧急联系人（相对稳定）
    emergency_contact_name = Column(String(64), doc="紧急联系人姓名")
    emergency_contact_phone = Column(String(32), doc="紧急联系人电话")
    emergency_address = Column(String(256), doc="紧急联系人地址")

    # 时间戳
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="更新时间",
    )

    # 约束和索引
    __table_args__ = (
        # 修改：允许同一用户同一护照申请多次
        UniqueConstraint("user_id", "passport_number", name="uq_user_passport"),
        CheckConstraint(
            "sex IN ('MALE', 'FEMALE', 'M', 'F') OR sex IS NULL", name="check_sex"
        ),
        CheckConstraint(
            "date_of_expiry > date_of_issue OR date_of_expiry IS NULL OR date_of_issue IS NULL",
            name="check_passport_dates",
        ),
        Index(
            "ix_applicant_passport_lookup", "passport_number", "surname", "given_name"
        ),
        Index("ix_applicant_user_created", "user_id", "created_at"),
    )

    @validates("sex")
    def validate_sex(self, key, sex):
        if sex and sex not in ["MALE", "FEMALE", "M", "F"]:
            raise ValueError(f"Invalid sex value: {sex}")
        return sex

    # 注意：以下字段已移动到application表
    # - visa_entry_type, visa_validity_duration, visa_start_date
    # - intended_entry_gate, purpose_of_entry
    # - visited_vietnam_last_year, previous_entry_date, previous_exit_date, previous_purpose
    # - has_vietnam_contact, vietnam_contact_organization, vietnam_contact_phone, vietnam_contact_address, vietnam_contact_purpose
