import { api } from '@/api/request'
import type { StatisticsData } from '@/api/statistics'
import type { ApiResponse } from '@/api/types'
import { computed, onUnmounted, readonly, ref } from 'vue'

// 可选的配置参数
export interface UseStatisticsOptions {
  defaultPeriod?: string
}

export const useStatistics = (options: UseStatisticsOptions = {}) => {
  const { defaultPeriod = '7d' } = options

  // 状态管理
  const loading = ref(false)
  const error = ref<string | null>(null)
  const data = ref<StatisticsData | null>(null)
  const period = ref(defaultPeriod)
  const lastUpdated = ref<string | null>(null)

  // 获取统计数据
  const fetchStatistics = async (targetPeriod?: string) => {
    if (loading.value) {
      console.log('🔄 [统计] 正在加载中，跳过重复请求')
      return
    }

    try {
      loading.value = true
      error.value = null

      const periodToUse = targetPeriod ?? data.value?.period ?? defaultPeriod
      console.log(`📊 [统计] 获取统计数据，周期: ${periodToUse}`)

      const response = await api.get<ApiResponse<StatisticsData>>(
        `/api/visa/admin/statistics?period=${periodToUse}`,
      )

      if (response.success && response.data) {
        data.value = response.data
        lastUpdated.value = new Date().toISOString()

        // 如果传入了period参数，更新当前period
        if (targetPeriod) {
          period.value = periodToUse
        }

        console.log(`✅ [统计] 统计数据加载成功`)
      } else {
        const errorMsg = response.message ?? '获取统计数据失败'
        error.value = errorMsg
        console.warn(`❌ [统计] ${errorMsg}`)
      }
    } catch (err: unknown) {
      const errorMsg = err instanceof Error ? err.message : '网络请求失败'
      error.value = errorMsg
      console.error('💥 [统计] 获取统计数据异常:', err)
    } finally {
      loading.value = false
    }
  }

  // 计算属性
  const isReady = computed(() => data.value !== null && !loading.value)
  const isEmpty = computed(() => isReady.value && data.value?.basic_stats?.total_applications === 0)
  const hasData = computed(() => isReady.value && !isEmpty.value)

  // 基础统计数据
  const basicStats = computed(() => data.value?.basic_stats ?? null)
  const timeSeriesData = computed(() => data.value?.time_series ?? [])
  const statusDistribution = computed(() => data.value?.status_distribution ?? [])
  const paymentStats = computed(() => data.value?.payment_stats ?? null)
  const visaTypeStats = computed(() => data.value?.visa_type_stats ?? [])
  const processingTimeStats = computed(() => data.value?.processing_time_stats ?? null)

  // 手动刷新
  const refresh = () => {
    fetchStatistics(period.value)
  }

  // 切换时间周期
  const changePeriod = (newPeriod: string) => {
    period.value = newPeriod
    fetchStatistics(newPeriod)
  }

  // 获取状态文本
  const getStatusLabel = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: '待处理',
      processing: '处理中',
      success: '提交成功',
      failed: '提交失败',
      submitted: '已提交',
      approved: '已审批',
      rejected: '已拒签',
      cancelled: '已取消',
    }
    return statusMap[status] || status
  }

  // 设置默认统计数据（用于首次加载或错误情况）
  const setDefaultStatistics = () => {
    data.value = {
      basic_stats: {
        total_applications: 0,
        successful_applications: 0,
        failed_applications: 0,
        pending_applications: 0,
        success_rate: 0,
      },
      time_series: [],
      status_distribution: [],
      payment_stats: {
        paid_count: 0,
        pending_count: 0,
        failed_count: 0,
        total_amount: 0,
      },
      visa_type_stats: [],
      processing_time_stats: {
        average_hours: 0,
        median_hours: 0,
        min_hours: 0,
        max_hours: 0,
        total_processed: 0,
      },
      period: period.value,
      last_updated: new Date().toISOString(),
    }
  }

  // 清理函数（移除定时器清理）
  const cleanup = () => {
    console.log('🔄 [统计] 组件清理完成')
  }

  // 在组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    period: readonly(period),
    lastUpdated: readonly(lastUpdated),

    // 计算属性
    isReady,
    isEmpty,
    hasData,
    basicStats,
    timeSeriesData,
    statusDistribution,
    paymentStats,
    visaTypeStats,
    processingTimeStats,

    // 方法 - 移除自动刷新，只保留手动触发
    fetchStatistics,
    refresh,
    changePeriod,
    getStatusLabel,
    setDefaultStatistics,
    cleanup,
  }
}
