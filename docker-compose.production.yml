# 🚀 生产环境配置 - 用于客户试用和生产部署
# 使用方式：docker compose -f docker-compose.yml -f docker-compose.production.yml up -d
#
# 🎯 生产环境特点：
# - 完全容器化，包括前端
# - 使用nginx统一入口（端口8000）
# - 适合frp隧道暴露到公网
# - 适合客户试用和生产部署
# - 禁用调试功能，优化性能和安全性

services:
  # 前端服务 - 生产环境使用容器化版本
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=/api  # 使用相对路径，适用于容器环境
    environment:
      - NODE_ENV=production

  # Nginx负载均衡器 - 生产环境启用
  nginx-lb:
    image: nginx:latest
    container_name: visa_automator_nginx_lb
    ports:
      - "8000:8000"  # 生产环境使用8000端口（用于frp隧道）
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - visa-automator
      - frontend
    restart: unless-stopped
    networks:
      - app-network

  # 后端服务 - 生产环境配置
  visa-automator:
    # 🔒 2025年安全增强：只读文件系统 + tmpfs（可选）
    read_only: ${DOCKER_SECURITY_READONLY:-false}
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=200m
      - /tmp:noexec,nosuid,size=100m
    environment:
      - ENVIRONMENT=production
      - DEBUG=false

  # Celery工作者 - 生产环境配置
  celery-worker:
    # 🔒 2025年安全增强：只读文件系统 + tmpfs（可选）
    read_only: ${DOCKER_SECURITY_READONLY:-false}
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=200m
      - /tmp:noexec,nosuid,size=100m
    environment:
      - ENVIRONMENT=production

  # 邮件轮询服务 - 生产环境配置
  email-polling:
    # 🔒 2025年安全增强：只读文件系统 + tmpfs（可选）
    read_only: ${DOCKER_SECURITY_READONLY:-false}
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m
      - /app/temp:noexec,nosuid,size=100m
      - /tmp:noexec,nosuid,size=100m
    environment:
      - ENVIRONMENT=production

  # E2E测试服务（可选）
  playwright:
    build:
      context: ./frontend
      dockerfile: Dockerfile.playwright
      args:
        - VITE_API_URL=http://nginx-lb:8000
    depends_on:
      - nginx-lb
    environment:
      - CI=true
      - PLAYWRIGHT_HTML_HOST=0.0.0.0
    volumes:
      - ./frontend/test-results:/app/test-results
      - ./frontend/playwright-report:/app/playwright-report
    ports:
      - "9323:9323"  # Playwright报告服务器
    profiles:
      - e2e  # 只在需要E2E测试时启用
    networks:
      - app-network
