"""
Celery 任务定义

包含签证申请处理等异步任务
🔥 新架构：Celery专注业务逻辑，通过HTTP API通知FastAPI进行数据库操作
✅ 职责分离：Celery不直接操作数据库，避免事件循环冲突
✅ 实时通知：任务开始和完成时主动通知FastAPI更新automation_logs
✅ 数据库更新：由FastAPI统一处理，确保事务一致性和架构清晰
"""

import logging
import os
from pathlib import Path
import sys
from typing import Any

from celery import shared_task
from celery.signals import task_revoked

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

# 全局变量存储当前任务信息，用于取消时更新状态
current_task_info: dict[str, Any] = {}


# 🔥 新架构：FastAPI通知函数
def notify_task_started(task_id: str, order_no: str, user_id: str):
    """通知FastAPI任务开始"""
    try:
        import os

        import requests

        # 🔐 修复HTTP不安全传输风险 - 内部Docker网络使用HTTP（开发阶段安全）
        # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
        api_base_url = os.getenv("INTERNAL_API_BASE_URL", "http://visa-automator:8000")

        # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
        response = requests.post(
            # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
            f"{api_base_url}/api/automation-logs/internal/task-started",
            json={
                "task_id": task_id,
                "order_no": order_no,
                "user_id": user_id,
                "task_type": "vietnam_evisa",
            },
            timeout=5,
        )
        if response.status_code == 200:
            logger.info(f"✅ 已通知FastAPI任务开始celery_task_id: {task_id}")
        else:
            logger.warning(f"⚠️ 通知任务开始失败: {response.status_code}")
    except Exception as e:
        logger.warning(f"⚠️ 通知任务开始异常: {e}")


def notify_task_completed(
    task_id: str,
    status: str,
    result: dict | None = None,
    error_message: str | None = None,
):
    """通知FastAPI任务完成"""
    try:
        import os

        import requests

        # 🔐 修复HTTP不安全传输风险 - 内部Docker网络使用HTTP（开发阶段安全）
        # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
        api_base_url = os.getenv("INTERNAL_API_BASE_URL", "http://visa-automator:8000")

        # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
        response = requests.post(
            # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
            f"{api_base_url}/api/automation-logs/internal/task-completed",
            json={
                "task_id": task_id,
                "status": status,
                "result": result,
                "error_message": error_message,
            },
            timeout=5,
        )
        if response.status_code == 200:
            logger.info(
                f"✅ 已通知FastAPI任务完成celery_task_id: {task_id} -> {status}"
            )
        else:
            logger.warning(f"⚠️ 通知任务完成失败: {response.status_code}")
    except Exception as e:
        logger.warning(f"⚠️ 通知任务完成异常: {e}")


@task_revoked.connect
def task_revoked_handler(sender=None, **kwargs):
    """
    处理任务被取消的信号
    ✅ 当任务被 revoke 时自动更新 automation_logs 状态
    """
    try:
        # 获取任务信息
        request = kwargs.get("request", {})
        task_id = request.get("id")
        args = request.get("args", [])

        logger.info(f"📢 收到任务取消信号: celery_task_id={task_id}")

        if not task_id or len(args) < 2:
            logger.warning(
                f"⚠️ 任务信息不完整: celery_task_id={task_id}, args={len(args)}"
            )
            return

        order_no, user_id = args[0], args[1]
        logger.info(f"📝 订单信息: {order_no}, 用户id: {user_id}")

        # 🔥 新架构：通过FastAPI通知任务取消
        try:
            import os

            import requests

            # 🔐 修复HTTP不安全传输风险 - 内部Docker网络使用HTTP（开发阶段安全）
            # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
            api_base_url = os.getenv(
                "INTERNAL_API_BASE_URL", "http://visa-automator:8000"
            )

            # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
            requests.post(
                # nosemgrep: python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http
                f"{api_base_url}/api/automation-logs/internal/task-completed",
                json={
                    "task_id": task_id,
                    "status": "cancelled",
                    "error_message": "用户主动取消申请",
                },
                timeout=5,
            )
            logger.info(f"✅ 已通知FastAPI任务取消celery_task_id: {task_id}")
        except Exception as e:
            logger.warning(f"⚠️ 通知任务取消失败: {e}")

        logger.info(
            f"✅ 已通知FastAPI更新automation_logs状态为cancelled,celery_task_id: {task_id}"
        )

        # 清理任务信息（如果存在）
        current_task_info.pop(task_id, None)

    except Exception as e:
        logger.error(f"❌ 处理任务取消信号失败: {e}", exc_info=True)


@shared_task(bind=True, name="celery_worker.tasks.process_visa_application")
def process_visa_application(
    self, order_no: str, user_id: str, application_data: dict[str, Any]
):
    """
    处理签证申请的异步任务

    Args:
        order_no: 订单编号
        user_id: 用户ID
        application_data: 完整的申请数据（包含表单和文件信息）

    Returns:
        Dict: 处理结果
    """
    try:
        logger.info(f"🚀 开始处理签证申请任务: {self.request.id}, 订单编号: {order_no}")

        # 记录任务信息，用于取消时更新状态
        current_task_info[self.request.id] = {"order_no": order_no, "user_id": user_id}

        # 🔥 新架构：通知FastAPI任务开始
        notify_task_started(self.request.id, order_no, user_id)

        # 解析申请数据
        form_data = application_data.get("form_data", {})
        file_info = application_data.get("file_info", {})

        # 获取API创建的临时文件路径（原始简单方法）
        temp_photo_path = file_info.get("portrait_photo_path")
        temp_passport_path = file_info.get("passport_scan_path")

        logger.info(f"[{self.request.id}] 使用临时文件:")
        logger.info(f"[{self.request.id}] Portrait: {temp_photo_path}")
        logger.info(f"[{self.request.id}] Passport: {temp_passport_path}")

        # 导入必需模块（放在靠近使用的地方，避免在模块加载时触发环境依赖）
        try:
            # 🔥 关键修改：将所有导入移到函数内部，避免模块加载时触发异步导入
            # 🔥 构建导入VisaAutomationEngine，避免在模块加载时触发异步导入
            from app.core.visa_automation_engine import VisaAutomationEngine
            from app.data.model import VietnamEVisaApplicant

            # 创建签证自动化引擎实例
            engine = VisaAutomationEngine()

        except ImportError as import_error:
            logger.error(f"❌ 模块导入失败: {import_error}")
            # 只对系统级导入错误进行快速重试（1次，10秒间隔）
            if self.request.retries < 1:
                logger.info(f"🔄 模块导入重试: {self.request.id}")
                raise self.retry(exc=import_error, countdown=10, max_retries=1)
            else:
                logger.error(f"❌ 模块导入最终失败: {import_error}")
                raise import_error

        try:
            # 创建VietnamEVisaApplicant对象
            internal_applicant = VietnamEVisaApplicant(
                customer_source=form_data.get("customer_source"),
                surname=form_data.get("surname", ""),
                given_name=form_data.get("given_name", ""),
                chinese_name=form_data.get("chinese_name", ""),
                sex=form_data.get("sex", ""),
                dob=form_data.get("dob", ""),
                place_of_birth=form_data.get("place_of_birth", ""),
                nationality=form_data.get("nationality", "CHINA"),
                religion=form_data.get("religion", "NO"),
                passport_number=form_data.get("passport_number", ""),
                passport_type=form_data.get("passport_type", "Ordinary passport"),
                place_of_issue=form_data.get("place_of_issue", ""),
                date_of_issue=form_data.get("date_of_issue", ""),
                passport_expiry=form_data.get("passport_expiry", ""),
                email=form_data.get("email", ""),
                telephone_number=form_data.get("telephone_number", ""),
                permanent_address=form_data.get("permanent_address", ""),
                contact_address=form_data.get("contact_address", ""),
                emergency_contact_name=form_data.get("emergency_contact_name", ""),
                emergency_address=form_data.get("emergency_address", ""),
                emergency_contact_phone=form_data.get("emergency_contact_phone", ""),
                visa_entry_type=form_data.get("visa_entry_type", ""),
                visa_validity_duration=form_data.get("visa_validity_duration", ""),
                visa_start_date=form_data.get("visa_start_date", ""),
                intended_entry_gate=form_data.get("intended_entry_gate", ""),
                purpose_of_entry=form_data.get("purpose_of_entry", "Tourist"),
                visited_vietnam_last_year=form_data.get(
                    "visited_vietnam_last_year", False
                ),
                previous_entry_date=form_data.get("previous_entry_date", ""),
                previous_exit_date=form_data.get("previous_exit_date", ""),
                previous_purpose=form_data.get("previous_purpose", ""),
                has_vietnam_contact=form_data.get("has_vietnam_contact", False),
                vietnam_contact_organization=form_data.get(
                    "vietnam_contact_organization", ""
                ),
                vietnam_contact_phone=form_data.get("vietnam_contact_phone", ""),
                vietnam_contact_address=form_data.get("vietnam_contact_address", ""),
                vietnam_contact_purpose=form_data.get("vietnam_contact_purpose", ""),
                portrait_photo_path=temp_photo_path,
                passport_scan_path=temp_passport_path,
            )

            # 设置强制重新提交标识
            if form_data.get("force_resubmit") == "true":
                internal_applicant.force_resubmit = True
                logger.info(f"[{self.request.id}] 用户选择强制重新提交")

            # 🔥 任务开始通知已在上面调用notify_task_started

            # 执行自动化任务
            logger.info(f"[{self.request.id}] 开始执行越南签证自动化填表...")
            automation_succeeded = engine.run_vietnam_evisa_step1(internal_applicant)

            if automation_succeeded:
                # 获取结果
                vietnam_application_number = getattr(
                    internal_applicant, "application_number", None
                )
                payment_amount = getattr(internal_applicant, "payment_amount", None)
                is_duplicate = getattr(
                    internal_applicant, "is_duplicate_submission", False
                )

                result = {
                    "task_id": self.request.id,
                    "order_no": order_no,
                    "status": "success",
                    "message": "签证申请自动化处理完成",
                    "application_number": vietnam_application_number,
                    "payment_amount": payment_amount,
                    "is_duplicate": is_duplicate,
                    "applicant_info": {
                        "name": f"{form_data.get('surname', '')} {form_data.get('given_name', '')}".strip(),  # type: ignore[attr-defined]
                        "chinese_name": form_data.get("chinese_name", ""),  # type: ignore[attr-defined]
                        "email": form_data.get("email", ""),  # type: ignore[attr-defined]
                    },
                }

                logger.info(f"✅ 签证申请任务完成: {self.request.id}")
                logger.info(f"✅ 越南官网申请编号: {vietnam_application_number}")
                logger.info(f"✅ 付款金额: {payment_amount}")

                # 🔥 新架构：通知FastAPI任务完成
                notify_task_completed(self.request.id, "success", result)

                # 清理任务信息
                current_task_info.pop(self.request.id, None)

                return result
            else:
                # 自动化失败，但可能已经获得申请编号
                # 🔧 重要：即使失败也要获取申请编号，因为可能填表成功但付款失败
                vietnam_application_number = getattr(
                    internal_applicant, "application_number", None
                )

                error_message = "自动化填表或付款失败"
                logger.error(
                    f"❌ 签证申请任务失败: {self.request.id} - {error_message}"
                )

                result = {
                    "task_id": self.request.id,
                    "order_no": order_no,
                    "status": "failed",
                    "message": error_message,
                    "error_type": "automation_failed",
                    # 🔧 新增：即使失败也返回申请编号（如果有的话）
                    "application_number": vietnam_application_number,
                    "applicant_info": {
                        "name": f"{form_data.get('surname', '')} {form_data.get('given_name', '')}".strip(),
                        "chinese_name": form_data.get("chinese_name", ""),
                        "email": form_data.get("email", ""),
                    },
                }

                # 🔥 新架构：通知FastAPI任务失败
                notify_task_completed(self.request.id, "failed", result, error_message)

                # 清理任务信息
                current_task_info.pop(self.request.id, None)

                return result

        finally:
            # 清理临时文件
            if temp_photo_path and Path(temp_photo_path).exists():
                Path(temp_photo_path).unlink()
                logger.info(f"[{self.request.id}] 清理证件照: {temp_photo_path}")
            if temp_passport_path and Path(temp_passport_path).exists():
                Path(temp_passport_path).unlink()
                logger.info(f"[{self.request.id}] 清理护照扫描: {temp_passport_path}")

    except Exception as exc:
        logger.error(
            f"❌ 签证申请任务异常: {self.request.id}, 错误: {str(exc)}", exc_info=True
        )

        # 移除Celery层重试：核心引擎已有完善的重试机制，避免重复重试
        # 业务逻辑错误应该由核心引擎层处理，而不是在Celery层重试
        logger.info(f"❌ 自动化任务失败: {self.request.id}")

        # 🔧 重要：即使异常也尝试获取申请编号，因为可能在后续步骤出错
        vietnam_application_number = None
        try:
            if "internal_applicant" in locals():
                vietnam_application_number = getattr(
                    internal_applicant, "application_number", None
                )
        except Exception:
            pass  # 忽略获取申请编号时的错误

        # 确保清理文件
        try:
            temp_photo_path = file_info.get("portrait_photo_path")
            temp_passport_path = file_info.get("passport_scan_path")
            if temp_photo_path and Path(temp_photo_path).exists():
                Path(temp_photo_path).unlink()
            if temp_passport_path and Path(temp_passport_path).exists():
                Path(temp_passport_path).unlink()
        except Exception as cleanup_error:
            logger.warning(f"清理文件失败: {cleanup_error}")

        result = {
            "task_id": self.request.id,
            "order_no": order_no,
            "status": "failed",
            "message": f"任务执行异常: {str(exc)}",
            "error_type": "exception",
            "retries": self.request.retries,
            # 🔧 新增：即使异常也返回申请编号（如果有的话）
            "application_number": vietnam_application_number,
            "applicant_info": {
                "name": f"{form_data.get('surname', '')} {form_data.get('given_name', '')}".strip(),
                "chinese_name": form_data.get("chinese_name", ""),
                "email": form_data.get("email", ""),
            },
        }

        # 🔥 新架构：通知FastAPI任务异常
        notify_task_completed(self.request.id, "failed", result, str(exc))

        # 清理任务信息
        current_task_info.pop(self.request.id, None)

        return result


# 🔥 旧函数已删除，改用notify_task_completed通知FastAPI


@shared_task(name="celery_worker.tasks.health_check")
def health_check():
    """
    Celery健康检查任务
    """
    return {"status": "healthy", "message": "Celery worker is running"}


@shared_task(name="celery_worker.tasks.health_check_with_db")
def health_check_with_db():
    """
    简化的健康检查任务 - 新架构下Celery不直接访问数据库
    """
    try:
        # 🔥 新架构：Celery不直接测试数据库，只检查自身状态
        return {
            "status": "healthy",
            "message": "Celery worker is running (database access via FastAPI)",
            "database_connection": "managed_by_fastapi",
            "worker_id": health_check_with_db.request.id  # type: ignore[attr-defined]
            if hasattr(health_check_with_db, "request")
            else "unknown",
        }
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"Health check failed: {str(e)}",
            "database_connection": "unknown",
        }
