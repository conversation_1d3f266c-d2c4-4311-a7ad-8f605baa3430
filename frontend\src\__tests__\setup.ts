/**
 * 测试环境设置
 * ============
 *
 * 配置测试环境，包括全局mocks和polyfills
 */

import '@testing-library/jest-dom'
import { beforeEach, vi } from 'vitest'

// 全局清理
beforeEach(() => {
  vi.clearAllMocks()
})

// Mock Element Plus组件和样式
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
  ElNotification: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
  ElLoading: {
    service: vi.fn(() => ({
      close: vi.fn(),
    })),
  },
}))

// Mock CSS imports
vi.mock('element-plus/dist/index.css', () => ({}))
vi.mock('element-plus/theme-chalk/index.css', () => ({}))

// Mock SCSS imports
vi.mock('*.scss', () => ({}))
vi.mock('*.css', () => ({}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    pathname: '/',
    search: '',
    hash: '',
    reload: vi.fn(),
    assign: vi.fn(),
    replace: vi.fn(),
  },
  writable: true,
})

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Vue Test Utils全局配置
import { config } from '@vue/test-utils'

config.global.stubs = {
  transition: false,
  'transition-group': false,
  // Element Plus组件stubs
  ElButton: { template: '<button><slot /></button>' },
  ElInput: { template: '<input />' },
  ElForm: { template: '<form><slot /></form>' },
  ElFormItem: { template: '<div><slot /></div>' },
  ElDatePicker: { template: '<input type="date" />' },
  ElSelect: { template: '<select><slot /></select>' },
  ElOption: { template: '<option><slot /></option>' },
  ElRadio: { template: '<input type="radio" />' },
  ElRadioGroup: { template: '<div><slot /></div>' },
  ElCheckbox: { template: '<input type="checkbox" />' },
  ElUpload: { template: '<div><slot /></div>' },
  ElCard: { template: '<div><slot /></div>' },
  ElRow: { template: '<div><slot /></div>' },
  ElCol: { template: '<div><slot /></div>' },
  // Router Link
  RouterLink: { template: '<a><slot /></a>' },
  RouterView: { template: '<div><slot /></div>' },
}
