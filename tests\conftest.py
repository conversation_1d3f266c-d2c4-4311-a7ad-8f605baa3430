"""
测试配置文件 - 修复版本
修复了async配置和mock设置问题
"""

import asyncio
from datetime import date, datetime
import os
from unittest.mock import AsyncMock, Mock
import uuid

from fastapi import FastAPI
from fastapi.testclient import TestClient
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

# 设置测试环境变量
os.environ["ENVIRONMENT"] = "testing"
os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///:memory:"
os.environ["SECRET_KEY"] = "test-secret-key"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"

# 🔧 数据库配置（修复安全检查失败）
os.environ["POSTGRES_PASSWORD"] = "test_password"
os.environ["POSTGRES_USER"] = "test_user"
os.environ["POSTGRES_DB"] = "test_visa_db"
os.environ["POSTGRES_HOST"] = "localhost"
os.environ["POSTGRES_PORT"] = "5432"

# API Basic Authentication - Required for Security
os.environ["API_BASIC_USERS"] = "test_user,admin_user"
os.environ["API_BASIC_PASSWORDS"] = "test_password_123,admin_password_456"


# 配置pytest-asyncio
pytest_asyncio.fixture(scope="session", autouse=True)


async def setup_test_environment():
    """设置测试环境"""
    pass


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_async_session():
    """创建模拟异步会话"""
    session = AsyncMock(spec=AsyncSession)

    # 配置常用的异步方法
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    session.add = Mock()
    session.delete = Mock()
    session.refresh = AsyncMock()
    session.merge = AsyncMock()

    # 配置查询结果
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = AsyncMock()
    mock_result.scalars = AsyncMock()
    mock_result.fetchall = AsyncMock()
    mock_result.fetchone = AsyncMock()
    session.execute.return_value = mock_result

    return session


@pytest.fixture
def mock_user():
    """创建测试用户"""
    from backend.auth_fastapi_users.models import User

    user = Mock(spec=User)
    user.id = uuid.uuid4()
    user.email = "<EMAIL>"
    user.username = "testuser"
    user.is_active = True
    user.is_superuser = False
    user.is_verified = True
    user.created_at = datetime.utcnow()
    user.updated_at = datetime.utcnow()
    user.hashed_password = "hashed_password"

    return user


@pytest.fixture
def mock_superuser():
    """创建测试超级用户"""
    from backend.auth_fastapi_users.models import User

    user = Mock(spec=User)
    user.id = uuid.uuid4()
    user.email = "<EMAIL>"
    user.username = "admin"
    user.is_active = True
    user.is_superuser = True
    user.is_verified = True
    user.created_at = datetime.utcnow()
    user.updated_at = datetime.utcnow()
    user.hashed_password = "hashed_password"

    return user


@pytest.fixture
def mock_applicant():
    """创建测试申请人"""
    try:
        from app.data.models import Applicant

        spec = Applicant
    except ImportError:
        spec = None

    applicant = Mock(spec=spec)
    applicant.id = uuid.uuid4()
    applicant.passport_number = "E12345678"
    applicant.surname = "ZHANG"
    applicant.given_name = "WEI"
    applicant.sex = "Male"
    applicant.dob = date(1990, 1, 1)
    applicant.nationality = "China"
    applicant.passport_expiry = date(2030, 1, 1)
    applicant.contact_address = "123 Main St, Beijing, China"
    applicant.created_at = datetime.utcnow()
    applicant.updated_at = datetime.utcnow()

    return applicant


@pytest.fixture
def mock_application():
    """创建测试申请"""
    try:
        from app.data.models import Application

        spec = Application
    except ImportError:
        spec = None

    application = Mock(spec=spec)
    application.id = uuid.uuid4()
    application.applicant_id = uuid.uuid4()
    application.country_code = "VN"
    application.visa_category = "tourist"
    application.visa_entry_type = "Single-entry"
    application.visa_validity_duration = "30"
    application.visa_start_date = date(2024, 8, 1)
    application.intended_entry_gate = "Tan Son Nhat International Airport"
    application.purpose_of_entry = "Tourist"
    application.expedited_type = "normal"
    application.status = "created"
    application.created_at = datetime.utcnow()
    application.updated_at = datetime.utcnow()

    return application


@pytest.fixture
def mock_order():
    """创建测试订单"""
    try:
        from app.data.models import Order

        spec = Order
    except ImportError:
        spec = None

    order = Mock(spec=spec)
    order.id = uuid.uuid4()
    order.order_no = "VN202407050001"
    order.user_id = uuid.uuid4()
    order.applicant_id = uuid.uuid4()
    order.application_id = uuid.uuid4()
    order.status = "created"
    order.customer_source = "website"
    order.created_at = datetime.utcnow()
    order.updated_at = datetime.utcnow()

    return order


@pytest.fixture
def sample_form_data():
    """创建示例表单数据"""
    return {
        "order_no": "VN202407050001",
        "surname": "ZHANG",
        "given_name": "WEI",
        "sex": "Male",
        "dob": "1990-01-01",
        "nationality": "China",
        "passport_number": "E12345678",
        "passport_expiry": "2030-01-01",
        "visa_category": "tourist",
        "visa_entry_type": "Single-entry",
        "visa_validity_duration": "30",
        "visa_start_date": "2024-08-01",
        "intended_entry_gate": "Tan Son Nhat International Airport",
        "contact_address": "123 Main St, Beijing, China",
        "purpose_of_entry": "Tourist",
        "expedited_type": "normal",
    }


@pytest.fixture
def mock_order_service():
    """创建模拟订单服务"""
    service = AsyncMock()

    # 配置常用方法
    service.create_order = AsyncMock()
    service.query_user_orders = AsyncMock()
    service.get_order_detail = AsyncMock()
    service.update_order_status = AsyncMock()
    service.cancel_order = AsyncMock()
    service.get_order_statistics = AsyncMock()

    # 配置默认返回值
    service.create_order.return_value = {
        "success": True,
        "data": {
            "order_no": "VN202407050001",
            "id": str(uuid.uuid4()),
            "status": "created",
        },
        "message": "订单创建成功",
    }

    service.query_user_orders.return_value = {
        "success": True,
        "data": {"orders": [], "total": 0, "page": 1, "limit": 20},
    }

    return service


@pytest.fixture
def mock_application_service():
    """创建模拟申请服务"""
    service = AsyncMock()

    # 配置常用方法
    service.submit_application = AsyncMock()
    service.cancel_application = AsyncMock()
    service.get_application_status = AsyncMock()
    service.update_application = AsyncMock()

    # 配置默认返回值
    service.submit_application.return_value = {
        "success": True,
        "message": "申请提交成功",
    }

    service.cancel_application.return_value = {
        "success": True,
        "message": "申请取消成功",
    }

    return service


@pytest.fixture
def mock_automation_service():
    """创建模拟自动化服务"""
    service = AsyncMock()

    # 配置常用方法
    service.start_automation = AsyncMock()
    service.get_task_status = AsyncMock()
    service.cancel_task = AsyncMock()
    service.get_task_logs = AsyncMock()

    # 配置默认返回值
    service.get_task_status.return_value = {
        "success": True,
        "data": {"status": "completed", "progress": 100, "message": "任务完成"},
    }

    return service


@pytest.fixture
def mock_repositories():
    """创建模拟仓库"""
    repositories = Mock()

    # 申请人仓库
    repositories.applicant = AsyncMock()
    repositories.applicant.create = AsyncMock()
    repositories.applicant.get_by_id = AsyncMock()
    repositories.applicant.get_by_passport = AsyncMock()
    repositories.applicant.update = AsyncMock()
    repositories.applicant.delete = AsyncMock()
    repositories.applicant.search = AsyncMock()

    # 申请仓库
    repositories.application = AsyncMock()
    repositories.application.create = AsyncMock()
    repositories.application.get_by_id = AsyncMock()
    repositories.application.update = AsyncMock()
    repositories.application.delete = AsyncMock()

    # 订单仓库
    repositories.order = AsyncMock()
    repositories.order.create = AsyncMock()
    repositories.order.get_by_id = AsyncMock()
    repositories.order.get_by_order_no = AsyncMock()
    repositories.order.update = AsyncMock()
    repositories.order.delete = AsyncMock()
    repositories.order.query_user_orders = AsyncMock()

    return repositories


@pytest.fixture
def test_app():
    """创建测试应用"""
    app = FastAPI(title="Test App")

    # 添加基本路由
    @app.get("/health")
    async def health_check():
        return {"status": "ok"}

    @app.get("/")
    async def root():
        return {"message": "Test API"}

    return app


@pytest.fixture
def test_client(test_app):
    """创建测试客户端"""
    return TestClient(test_app)


@pytest.fixture
def mock_database_manager():
    """创建模拟数据库管理器"""
    manager = AsyncMock()

    # 配置会话管理
    manager.get_session = AsyncMock()
    manager.close_session = AsyncMock()
    manager.create_tables = AsyncMock()
    manager.drop_tables = AsyncMock()

    # 配置会话返回值
    mock_session = AsyncMock(spec=AsyncSession)
    manager.get_session.return_value = mock_session

    return manager


@pytest.fixture
def mock_browser_manager():
    """创建模拟浏览器管理器"""
    manager = AsyncMock()

    # 配置浏览器操作
    manager.create_browser = AsyncMock()
    manager.create_page = AsyncMock()
    manager.close_browser = AsyncMock()
    manager.close_page = AsyncMock()

    # 配置模拟页面
    mock_page = AsyncMock()
    mock_page.goto = AsyncMock()
    mock_page.fill = AsyncMock()
    mock_page.click = AsyncMock()
    mock_page.screenshot = AsyncMock()
    mock_page.wait_for_selector = AsyncMock()
    mock_page.evaluate = AsyncMock()

    manager.create_page.return_value = mock_page

    return manager


@pytest.fixture
def mock_redis_client():
    """创建模拟Redis客户端"""
    client = AsyncMock()

    # 配置Redis操作
    client.get = AsyncMock()
    client.set = AsyncMock()
    client.delete = AsyncMock()
    client.exists = AsyncMock()
    client.expire = AsyncMock()
    client.hget = AsyncMock()
    client.hset = AsyncMock()
    client.hdel = AsyncMock()

    # 配置默认返回值
    client.get.return_value = None
    client.set.return_value = True
    client.delete.return_value = 1
    client.exists.return_value = False

    return client


@pytest.fixture
def mock_file_storage():
    """创建模拟文件存储"""
    storage = AsyncMock()

    # 配置文件操作
    storage.save_file = AsyncMock()
    storage.delete_file = AsyncMock()
    storage.get_file_url = AsyncMock()
    storage.file_exists = AsyncMock()

    # 配置默认返回值
    storage.save_file.return_value = "test_file_path.jpg"
    storage.delete_file.return_value = True
    storage.get_file_url.return_value = "http://example.com/file.jpg"
    storage.file_exists.return_value = True

    return storage


@pytest.fixture
def mock_email_service():
    """创建模拟邮件服务"""
    service = AsyncMock()

    # 配置邮件操作
    service.send_email = AsyncMock()
    service.send_notification = AsyncMock()
    service.send_batch_emails = AsyncMock()

    # 配置默认返回值
    service.send_email.return_value = {"success": True, "message_id": "test_message_id"}
    service.send_notification.return_value = {"success": True}

    return service


# 自动清理fixture
@pytest.fixture(autouse=True)
def cleanup_test_environment():
    """自动清理测试环境"""
    yield
    # 测试后清理工作
    pass


# 配置pytest插件
def pytest_configure(config):
    """配置pytest"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line("markers", "integration: marks tests as integration tests")
    config.addinivalue_line("markers", "unit: marks tests as unit tests")
    config.addinivalue_line("markers", "api: marks tests as API tests")
    config.addinivalue_line("markers", "auth: marks tests as authentication tests")


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加默认标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 异步测试支持
@pytest.fixture(scope="session")
def anyio_backend():
    """配置anyio后端"""
    return "asyncio"


# 数据库测试支持
@pytest_asyncio.fixture(scope="function")
async def test_db_session():
    """创建测试数据库会话"""
    # 使用内存数据库进行测试
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")

    # 创建数据库表
    from app.data.base import Base

    # 导入所有模型以确保它们被注册到Base.metadata

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建异步会话工厂
    from sqlalchemy.ext.asyncio import async_sessionmaker

    async_session_factory = async_sessionmaker(engine, expire_on_commit=False)

    # 创建会话
    async with async_session_factory() as session:
        yield session

    await engine.dispose()


# Alias for backward compatibility
@pytest_asyncio.fixture(scope="function")
async def db_session(test_db_session):
    """Alias for test_db_session to match test expectations"""
    return test_db_session


@pytest.fixture
def sample_user_data():
    """Create sample user data for tests"""
    return {
        "id": uuid.uuid4(),
        "username": f"testuser_{uuid.uuid4().hex[:8]}",
        "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
        "hashed_password": "hashed_password_test",
        "is_active": True,
        "is_superuser": False,
        "is_verified": True,
        "role": "user",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }


# 模拟外部服务
@pytest.fixture
def mock_external_apis():
    """模拟外部API服务"""
    apis = Mock()

    # 验证码服务
    apis.captcha_solver = AsyncMock()
    apis.captcha_solver.solve = AsyncMock()
    apis.captcha_solver.solve.return_value = "test_captcha_solution"

    # 支付服务
    apis.payment_service = AsyncMock()
    apis.payment_service.process_payment = AsyncMock()
    apis.payment_service.process_payment.return_value = {
        "success": True,
        "transaction_id": "test_transaction_id",
    }

    # 通知服务
    apis.notification_service = AsyncMock()
    apis.notification_service.send_sms = AsyncMock()
    apis.notification_service.send_email = AsyncMock()

    return apis
