import os

from celery import Celery

# 🔧 关键修复：初始化统一日志系统（UTC时区）
from app.utils.logger_config import get_logger, setup_logger

# 设置Celery Worker日志系统（UTC时区）
setup_logger(
    console_level="INFO", file_level="DEBUG", log_filename_prefix="celery_worker"
)
logger = get_logger()

# 从环境变量中获取Redis的主机名，这是Docker容器的服务名
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")

# 构建Redis的URL，使用不同的数据库以隔离Broker和Backend
# DB 0 for Broker, DB 1 for Result Backend
# 这种分离是最佳实践，可以防止两者相互干扰
BROKER_URL = f"redis://{REDIS_HOST}:6379/0"
RESULT_BACKEND_URL = f"redis://{REDIS_HOST}:6379/1"

# 创建Celery应用实例
# 第一个参数是当前模块的名称，建议保持为'celery_worker'
# 'include'参数告诉Celery去哪里寻找任务定义
app = Celery(
    "celery_worker",
    broker=BROKER_URL,
    backend=RESULT_BACKEND_URL,
    include=[
        "celery_worker.tasks",  # 包含任务模块
    ],
)

# 🔥 生产级配置 - 解决心跳丢失问题
app.conf.update(
    # ===== 时区配置 =====
    timezone="UTC",  # 明确使用UTC时区
    enable_utc=True,  # 启用UTC时区
    # ===== 基础任务配置 =====
    task_track_started=True,
    result_expires=3600,
    task_result_expires=3600,
    # ===== 心跳和连接配置 =====
    broker_heartbeat=60,  # 心跳间隔60秒（默认120秒太长）
    broker_heartbeat_checkrate=2.0,  # 心跳检查频率
    worker_prefetch_multiplier=1,  # 预取任务数量（减少预取，避免内存积累）
    # ===== 连接池配置 =====
    broker_pool_limit=10,  # Redis连接池大小
    broker_connection_retry=True,  # 启用连接重试
    broker_connection_retry_on_startup=True,  # 启动时重试连接
    broker_connection_max_retries=10,  # 最大重试次数
    # ===== 超时配置 =====
    task_soft_time_limit=600,  # 软超时10分钟（原来5分钟）
    task_time_limit=900,  # 硬超时15分钟（原来10分钟）
    worker_disable_rate_limits=True,  # 禁用速率限制
    # ===== 序列化配置 =====
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    # ===== 重试配置 =====
    task_acks_late=True,  # 延迟确认，确保任务完成后才确认
    # ===== Redis特定配置 =====
    redis_max_connections=50,  # Redis最大连接数
    redis_socket_keepalive=True,  # 启用TCP keepalive
    redis_socket_keepalive_options={  # TCP keepalive选项
        "TCP_KEEPINTVL": 1,
        "TCP_KEEPCNT": 3,
        "TCP_KEEPIDLE": 1,
    },
    # ===== 监控配置 =====
    worker_send_task_events=True,  # 发送任务事件
    task_send_sent_event=True,  # 发送任务发送事件
    # ===== 错误处理 =====
    task_reject_on_worker_lost=True,  # worker丢失时拒绝任务
    task_ignore_result=False,  # 不忽略结果
)

# 自动发现任务
# Celery会自动在 'include' 中指定的模块里寻找任务
# 这行代码虽然在这里不是严格必须的，因为我们已经在构造函数中使用了'include'，
# 但它是一个好的实践，可以确保所有任务都被加载。
app.autodiscover_tasks()

if __name__ == "__main__":
    # 这个入口点允许我们直接从命令行启动worker进行测试
    # 例如: celery -A celery_worker.celery_app worker --loglevel=info
    app.start()
