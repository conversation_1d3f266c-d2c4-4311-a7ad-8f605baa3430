import { api } from './request'
import type { SessionCheckResponse } from './types'

// 会话管理API
export const sessionApi = {
  // 检查会话状态，严格对应旧版SessionManager.checkSession
  checkSession: (): Promise<SessionCheckResponse> => {
    return api.get<SessionCheckResponse>('/api/session/check')
  },

  // 登出，清理会话
  logout: (): Promise<{ success: boolean }> => {
    return api.post<{ success: boolean }>('/api/session/logout')
  },
}
