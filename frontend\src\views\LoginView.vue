<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- 系统LOGO和标题 -->
        <div class="login-header">
          <div class="logo">
            <el-icon size="48" color="#409EFF">
              <Document />
            </el-icon>
          </div>
          <h1 class="system-title">越南签证自动化系统</h1>
          <p class="system-subtitle">欢迎回来，请登录您的账户</p>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item class="login-options">
            <div class="options-row">
              <el-checkbox v-model="loginForm.remember"> 记住我 </el-checkbox>
              <el-link type="primary" class="forgot-link"> 忘记密码？ </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="authStore.isLoading"
              @click="handleLogin"
            >
              {{ authStore.isLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 底部信息 -->
        <div class="login-footer">
          <p class="copyright">© 2025 越南签证自动化系统</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import type { LoginRequest } from '@/types/auth'
import { showErrorMessage } from '@/utils/notification'
import { Document, Lock, User } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由和状态管理
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 表单数据
const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
  remember: false,
})

// 表单验证规则
const loginRules = reactive<FormRules<LoginRequest>>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在 3 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度应在 6 到 50 个字符', trigger: 'blur' },
  ],
})

// 🔥 恢复：加载记住的用户名和记住我选项（不再自动填充密码）
const loadRememberedCredentials = () => {
  const remembered = authStore.getRememberedCredentials()
  if (remembered) {
    loginForm.username = remembered.username
    loginForm.remember = remembered.remember
  }
}

// 登录处理函数
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const success = await authStore.login(loginForm)
    if (success) {
      // 登录成功，跳转到目标页面或首页
      const redirectPath = (route.query.redirect as string) || '/'
      router.push(redirectPath)
    }
  } catch (error) {
    console.error('登录验证失败:', error)
    showErrorMessage('登录失败，请检查用户名和密码')
  }
}

// 🔥 新增：组件挂载时加载记住的登录信息
onMounted(() => {
  loadRememberedCredentials()
})

// 🔥 新增：监听authStore状态变化，在组件层显示通知
// 🔧 修复：移除不存在的authStore属性监听
// 错误处理已通过API拦截器和ElMessage统一处理
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.login-container {
  width: 100%;
  max-width: 400px;
  display: flex;
  justify-content: center;
}

.login-card {
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 12px 40px rgb(0 0 0 / 15%);
  padding: 40px 32px;
  box-sizing: border-box;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  margin-bottom: 16px;
}

.system-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px;
  line-height: 1.2;
}

.system-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-form .el-input {
  height: 48px;
}

.login-form :deep(.el-input__wrapper) {
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.login-form :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

.login-form :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 10%);
}

.login-options {
  margin-bottom: 24px;
}

.options-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.forgot-link {
  font-size: 14px;
  text-decoration: none;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(102 126 234 / 30%);
  transition: all 0.2s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgb(102 126 234 / 40%);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f3f4f6;
}

.copyright {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

/* 响应式设计 */
@media (width <= 480px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .system-title {
    font-size: 20px;
  }

  .login-form :deep(.el-input__wrapper) {
    padding: 10px 14px;
  }

  .login-button {
    height: 44px;
    font-size: 15px;
  }
}

/* 加载状态动画 */
.login-button.is-loading {
  position: relative;
}

.login-button.is-loading :deep(.el-loading-spinner) {
  margin-top: -12px;
}
</style>
