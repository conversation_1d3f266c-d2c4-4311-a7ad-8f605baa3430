#!/usr/bin/env python3
"""
测试运行器 - 修复版本
提供灵活的测试运行和报告功能
"""

import argparse
import os
from pathlib import Path
import subprocess
import sys


def run_command(cmd: list[str], cwd: Path | None = None) -> int:
    """运行命令并返回退出码"""
    print(f"Running: {' '.join(cmd)}")
    if cwd:
        print(f"Working directory: {cwd}")

    try:
        result = subprocess.run(cmd, cwd=cwd, check=False)
        return result.returncode
    except FileNotFoundError:
        print(f"Error: Command not found: {cmd[0]}")
        return 1
    except Exception as e:
        print(f"Error running command: {e}")
        return 1


def setup_environment():
    """设置测试环境"""
    print("Setting up test environment...")

    # 设置环境变量
    os.environ["ENVIRONMENT"] = "testing"
    os.environ["PYTHONPATH"] = str(Path.cwd())

    # 创建必要的目录
    Path("htmlcov").mkdir(exist_ok=True)
    Path("test-reports").mkdir(exist_ok=True)

    print("Test environment setup complete.")


def run_fixed_tests(test_type: str = "all", verbose: bool = False) -> int:
    """运行修复版本的测试"""
    print(f"Running {test_type} tests (fixed versions)...")

    cmd = ["python", "-m", "pytest"]

    # 基本参数
    if verbose:
        cmd.extend(["-v", "-s"])

    # 根据测试类型选择测试文件
    if test_type == "auth":
        cmd.append("tests/test_auth_fixed.py")
    elif test_type == "api":
        cmd.append("tests/test_api_routes_fixed.py")
    elif test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "fast":
        cmd.extend(["-m", "not slow"])
    elif test_type == "fixed":
        cmd.extend(["tests/test_auth_fixed.py", "tests/test_api_routes_fixed.py"])
    elif test_type == "all":
        cmd.append("tests/")
    else:
        print(f"Unknown test type: {test_type}")
        return 1

    # 添加覆盖率报告
    cmd.extend(
        [
            "--cov=app",
            "--cov=backend",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-report=xml:coverage.xml",
        ]
    )

    # 添加JUnit XML报告
    cmd.extend(["--junitxml=test-reports/junit.xml"])

    return run_command(cmd)


def run_original_tests(test_type: str = "all", verbose: bool = False) -> int:
    """运行原始版本的测试"""
    print(f"Running {test_type} tests (original versions)...")

    cmd = ["python", "-m", "pytest"]

    # 基本参数
    if verbose:
        cmd.extend(["-v", "-s"])

    # 排除修复版本的测试
    cmd.extend(
        [
            "--ignore=tests/test_auth_fixed.py",
            "--ignore=tests/test_api_routes_fixed.py",
            "--ignore=tests/conftest_fixed.py",
        ]
    )

    # 根据测试类型选择测试文件
    if test_type == "auth":
        cmd.append("tests/test_auth.py")
    elif test_type == "api":
        cmd.append("tests/test_api_routes.py")
    elif test_type == "models":
        cmd.append("tests/test_models.py")
    elif test_type == "services":
        cmd.append("tests/test_services.py")
    elif test_type == "all":
        cmd.append("tests/")
    else:
        print(f"Unknown test type: {test_type}")
        return 1

    # 添加覆盖率报告
    cmd.extend(
        [
            "--cov=app",
            "--cov=backend",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov-original",
            "--cov-report=xml:coverage-original.xml",
        ]
    )

    return run_command(cmd)


def run_comparison_tests(verbose: bool = False) -> int:
    """运行比较测试（原始vs修复）"""
    print("Running comparison tests...")

    print("\n" + "=" * 50)
    print("RUNNING ORIGINAL TESTS")
    print("=" * 50)
    original_result = run_original_tests("all", verbose)

    print("\n" + "=" * 50)
    print("RUNNING FIXED TESTS")
    print("=" * 50)
    fixed_result = run_fixed_tests("fixed", verbose)

    print("\n" + "=" * 50)
    print("COMPARISON RESULTS")
    print("=" * 50)
    print(f"Original tests exit code: {original_result}")
    print(f"Fixed tests exit code: {fixed_result}")

    if fixed_result == 0:
        print("✅ Fixed tests passed!")
    else:
        print("❌ Fixed tests failed!")

    if original_result == 0:
        print("✅ Original tests passed!")
    else:
        print("❌ Original tests failed!")

    return min(original_result, fixed_result)


def run_ci_tests() -> int:
    """运行CI环境测试"""
    print("Running CI tests...")

    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/",
        "--cov=app",
        "--cov=backend",
        "--cov-report=xml",
        "--cov-report=term",
        "--junitxml=test-reports/junit.xml",
        "--tb=short",
        "-q",
    ]

    return run_command(cmd)


def generate_test_report():
    """生成测试报告"""
    print("Generating test report...")

    # 检查覆盖率文件
    if Path("coverage.xml").exists():
        print("✅ Coverage report generated: coverage.xml")

    if Path("htmlcov/index.html").exists():
        print("✅ HTML coverage report generated: htmlcov/index.html")

    if Path("test-reports/junit.xml").exists():
        print("✅ JUnit XML report generated: test-reports/junit.xml")

    # 显示覆盖率摘要
    try:
        result = subprocess.run(
            ["python", "-m", "coverage", "report", "--show-missing"],
            capture_output=True,
            text=True,
        )
        if result.returncode == 0:
            print("\nCoverage Summary:")
            print(result.stdout)
    except Exception as e:
        print(f"Could not generate coverage summary: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Test runner for visa automation project"
    )

    parser.add_argument(
        "command",
        choices=["fixed", "original", "compare", "ci", "auth", "api", "all"],
        help="Test command to run",
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    parser.add_argument("--setup", action="store_true", help="Setup test environment")

    parser.add_argument("--report", action="store_true", help="Generate test report")

    args = parser.parse_args()

    # 设置环境
    if args.setup:
        setup_environment()

    # 运行测试
    exit_code = 0

    if args.command == "fixed":
        exit_code = run_fixed_tests("fixed", args.verbose)
    elif args.command == "original":
        exit_code = run_original_tests("all", args.verbose)
    elif args.command == "compare":
        exit_code = run_comparison_tests(args.verbose)
    elif args.command == "ci":
        exit_code = run_ci_tests()
    elif args.command == "auth":
        exit_code = run_fixed_tests("auth", args.verbose)
    elif args.command == "api":
        exit_code = run_fixed_tests("api", args.verbose)
    elif args.command == "all":
        exit_code = run_fixed_tests("all", args.verbose)

    # 生成报告
    if args.report:
        generate_test_report()

    # 显示结果
    if exit_code == 0:
        print("\n✅ Tests completed successfully!")
    else:
        print(f"\n❌ Tests failed with exit code: {exit_code}")

    return exit_code


if __name__ == "__main__":
    sys.exit(main())
