# backend/routes/visa/schemas.py
"""
签证申请相关的Pydantic数据模型

遵循QQ Notepad要求：
- 类型/Optional/默认值严格补全
- 保持API兼容性
- 详细的字段验证
"""

from typing import Any

from pydantic import BaseModel, Field

# ===== 基础数据模型 =====


class VisaFormFields(BaseModel):
    """签证申请表单字段模型 - 与applicant表结构完全一致"""

    # 个人基本信息
    surname: str = Field(..., description="姓")
    given_name: str = Field(..., description="名")
    chinese_name: str | None = Field(None, description="中文名")
    sex: str | None = Field(None, description="性别")
    nationality: str | None = Field("CHINA", description="国籍")
    date_of_birth: str | None = Field(None, description="出生日期，格式YYYY-MM-DD")
    place_of_birth: str | None = Field(None, description="出生地")
    # 联系方式
    telephone_number: str | None = Field(None, description="联系电话")
    email: str | None = Field(None, description="邮箱")
    permanent_address: str | None = Field(None, description="永久地址")
    contact_address: str | None = Field(None, description="联系地址")
    # 护照信息
    passport_number: str = Field(..., description="护照号")
    date_of_issue: str | None = Field(None, description="签发日期，格式YYYY-MM-DD")
    date_of_expiry: str | None = Field(None, description="到期日期，格式YYYY-MM-DD")
    place_of_issue: str | None = Field(None, description="签发地")
    # 工作信息
    work_unit: str | None = Field(None, description="工作单位")
    work_address: str | None = Field(None, description="工作地址")
    # 紧急联系人
    emergency_contact_name: str | None = Field(None, description="紧急联系人姓名")
    emergency_contact_phone: str | None = Field(None, description="紧急联系人电话")
    emergency_address: str | None = Field(None, description="紧急联系人地址")
    # 签证信息
    visa_entry_type: str | None = Field(None, description="签证入境类型")
    visa_validity_duration: str | None = Field(None, description="签证有效期（如30天）")
    visa_start_date: str | None = Field(
        None, description="签证生效日期，格式YYYY-MM-DD"
    )
    intended_entry_gate: str | None = Field(None, description="入境口岸")
    purpose_of_entry: str | None = Field("Tourist", description="入境目的")
    # 既往越南记录
    visited_vietnam_last_year: bool | None = Field(
        False, description="去年是否访问越南"
    )
    previous_entry_date: str | None = Field(
        None, description="上次入境日期，格式YYYY-MM-DD"
    )
    previous_exit_date: str | None = Field(
        None, description="上次出境日期，格式YYYY-MM-DD"
    )
    previous_purpose: str | None = Field(None, description="上次访问目的")
    # 越南联系人/组织
    has_vietnam_contact: bool | None = Field(False, description="是否有越南联系人")
    vietnam_contact_organization: str | None = Field(None, description="越南联系人组织")
    vietnam_contact_phone: str | None = Field(None, description="越南联系人电话")
    vietnam_contact_address: str | None = Field(None, description="越南联系人地址")
    vietnam_contact_purpose: str | None = Field(None, description="越南联系目的")
    # 控制字段
    force_resubmit: str | None = Field(None, description="强制重新提交标识")


class FileUploadInfo(BaseModel):
    """文件上传信息模型"""

    portrait_photo_filename: str | None = Field(None, description="证件照文件名")
    passport_scan_filename: str | None = Field(None, description="护照扫描文件名")
    portrait_photo_content_type: str | None = Field(None, description="证件照文件类型")
    passport_scan_content_type: str | None = Field(None, description="护照扫描文件类型")


class RequestMetadata(BaseModel):
    """请求元数据模型"""

    request_id: str = Field(..., description="请求ID")
    created_at: str = Field(..., description="创建时间")
    api_version: str = Field("v1", description="API版本")


class ApplicationData(BaseModel):
    """完整申请数据模型 - 与application表结构及主业务链路一致"""

    form_data: VisaFormFields = Field(
        ..., description="表单数据（与applicant表结构一致）"
    )
    visa_type_id: str = Field(..., description="签证类型ID（UUID）")
    order_id: str | None = Field(None, description="订单ID（UUID）")
    applicant_id: str | None = Field(None, description="申请人ID（UUID）")
    status: str | None = Field(None, description="当前状态")
    vietnam_application_number: str | None = Field(None, description="越南官方编号")
    created_at: str | None = Field(None, description="创建时间")
    updated_at: str | None = Field(None, description="更新时间")
    file_info: FileUploadInfo | None = Field(None, description="文件信息")
    request_metadata: RequestMetadata | None = Field(None, description="请求元数据")


# ===== 响应模型 =====


class TrackingInfo(BaseModel):
    """跟踪信息模型"""

    submission_time: str = Field(..., description="提交时间")
    expected_processing_days: str = Field("3-5", description="预期处理天数")
    email: str | None = Field(None, description="联系邮箱")
    order_no: str | None = Field(None, description="订单编号")
    application_number: str | None = Field(
        None, description="申请编号（通用字段，支持多国扩展）"
    )
    is_duplicate: bool | None = Field(False, description="是否重复提交")
    failure_reason: str | None = Field(None, description="失败原因")


class VisaApplicationResponse(BaseModel):
    """签证申请响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    application_id: str | None = Field(None, description="申请ID")
    status: str = Field(..., description="申请状态")
    tracking_info: TrackingInfo | None = Field(None, description="跟踪信息")


# ===== 状态查询模型 =====


class StatusStep(BaseModel):
    """状态步骤模型"""

    step: str = Field(..., description="步骤名称")
    status: str = Field(
        ..., description="步骤状态: completed, in_progress, pending, failed"
    )
    time: str | None = Field(None, description="步骤时间")
    description: str = Field(..., description="步骤描述")


class VisaStatusResponse(BaseModel):
    """签证状态响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    application_id: str = Field(..., description="申请ID")
    applicant_name: str = Field(..., description="申请人姓名")
    passport_number: str | None = Field(None, description="护照号码")
    status: str = Field(..., description="申请状态")
    approval_status: str = Field(..., description="审批状态")
    last_updated: str | None = Field(None, description="最后更新时间")
    visa_type: str | None = Field(None, description="签证类型")
    visa_validity_days: int | None = Field(None, description="签证有效天数")
    steps: list[StatusStep] = Field(default_factory=list, description="状态步骤")


# ===== 历史记录模型 =====


class HistoryApplication(BaseModel):
    """历史申请记录模型"""

    application_id: str | None = Field(None, description="申请ID")
    applicant_name: str = Field(..., description="申请人姓名")
    visa_type: str | None = Field(None, description="签证类型")
    visa_validity_days: int | None = Field(None, description="签证有效天数")
    visa_start_date: str | None = Field(None, description="签证开始日期")
    submit_status: str | None = Field(None, description="提交状态")
    approval_status: str | None = Field(None, description="审批状态")
    submission_time: str | None = Field(None, description="提交时间")
    last_updated: str | None = Field(None, description="最后更新时间")


class UserHistoryResponse(BaseModel):
    """用户历史记录响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    passport_number: str = Field(..., description="护照号码")
    applications: list[HistoryApplication] = Field(
        default_factory=list, description="申请记录列表"
    )


# ===== 管理员模型 =====


class PaginationInfo(BaseModel):
    """分页信息模型"""

    current_page: int = Field(..., description="当前页码")
    total_pages: int = Field(..., description="总页数")
    total_count: int = Field(..., description="总记录数")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class AdminQueryFilters(BaseModel):
    """管理员查询过滤器模型"""

    status: str | None = Field(None, description="状态筛选")
    date_from: str | None = Field(None, description="开始日期")
    date_to: str | None = Field(None, description="结束日期")
    search: str | None = Field(None, description="搜索关键词")


class AdminQueryResponse(BaseModel):
    """管理员查询响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: dict[str, Any] = Field(..., description="查询数据")


# ===== 统计模型 =====


class BasicStats(BaseModel):
    """基础统计模型"""

    total_applications: int = Field(0, description="总申请数")
    pending_applications: int = Field(0, description="待处理申请数")
    success_applications: int = Field(0, description="成功申请数")
    failed_applications: int = Field(0, description="失败申请数")


class StatisticsResponse(BaseModel):
    """统计响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: dict[str, Any] = Field(..., description="统计数据")


# ===== 导出模型 =====


class ExportFilters(BaseModel):
    """导出过滤器模型"""

    format: str = Field("csv", description="导出格式")
    date_from: str | None = Field(None, description="开始日期")
    date_to: str | None = Field(None, description="结束日期")
    status: str | None = Field(None, description="状态筛选")


# ===== 申请详情模型 =====


class BasicInfo(BaseModel):
    """基础信息模型"""

    application_number: str | None = Field(None, description="申请编号")
    submit_status: str | None = Field(None, description="提交状态")
    approval_status: str | None = Field(None, description="审批状态")
    created_at: str | None = Field(None, description="创建时间")
    updated_at: str | None = Field(None, description="更新时间")


class ApplicantInfo(BaseModel):
    """申请人信息模型"""

    chinese_name: str | None = Field(None, description="中文姓名")
    surname: str | None = Field(None, description="姓")
    given_name: str | None = Field(None, description="名")
    passport_number: str | None = Field(None, description="护照号码")
    nationality: str | None = Field(None, description="国籍")
    email: str | None = Field(None, description="邮箱")
    telephone_number: str | None = Field(None, description="电话号码")


class VisaInfo(BaseModel):
    """签证信息模型"""

    visa_entry_type: str | None = Field(None, description="签证入境类型")
    visa_validity_days: int | None = Field(None, description="签证有效天数")
    visa_start_date: str | None = Field(None, description="签证开始日期")
    intended_entry_gate: str | None = Field(None, description="预期入境口岸")
    purpose_of_entry: str | None = Field(None, description="入境目的")


class PaymentInfo(BaseModel):
    """支付信息模型"""

    payment_status: str | None = Field(None, description="支付状态")
    payment_amount: float | None = Field(None, description="支付金额")
    payment_time: str | None = Field(None, description="支付时间")


class ApplicationDetails(BaseModel):
    """申请详情模型"""

    basic_info: BasicInfo = Field(..., description="基础信息")
    applicant_info: ApplicantInfo = Field(..., description="申请人信息")
    visa_info: VisaInfo = Field(..., description="签证信息")
    payment_info: PaymentInfo = Field(..., description="支付信息")


class ApplicationDetailsResponse(BaseModel):
    """申请详情响应模型"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: ApplicationDetails = Field(..., description="申请详情")


# ===== 通用响应模型 =====


class SuccessResponse(BaseModel):
    """通用成功响应模型"""

    success: bool = Field(True, description="是否成功")
    message: str = Field(..., description="响应消息")
    data: dict[str, Any] | None = Field(None, description="响应数据")


class ErrorResponse(BaseModel):
    """通用错误响应模型"""

    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误消息")
    error_code: str | None = Field(None, description="错误代码")
    details: dict[str, Any] | None = Field(None, description="错误详情")


# ===== 重复检查模型 =====


class DuplicateCheckResponse(BaseModel):
    """重复检查响应模型"""

    success: bool = Field(..., description="是否成功")
    exists: bool = Field(..., description="是否存在")
    can_resubmit: bool | None = Field(None, description="是否允许重新提交")
    warning_type: str | None = Field(
        None, description="警告类型: success|pending|rejected|failed|none|error"
    )
    application_number: str | None = Field(None, description="申请编号")
    submission_time: str | None = Field(None, description="提交时间")
    status: str | None = Field(None, description="申请状态")
    approval_status: str | None = Field(None, description="审批状态")
    error: str | None = Field(None, description="错误信息")


# ===== 日期计算模型 =====


class CalculationInfo(BaseModel):
    """计算信息模型"""

    working_days: int = Field(..., description="工作日数量")
    calculated_from: str = Field("today", description="计算起点")
    timezone: str = Field("UTC", description="时区")
    description: str = Field(..., description="计算描述")


class ExpeditedDateResponse(BaseModel):
    """加急日期响应模型"""

    success: bool = Field(..., description="是否成功")
    expedited_date: str | None = Field(None, description="加急日期")
    calculation_info: CalculationInfo | None = Field(None, description="计算信息")
    error: str | None = Field(None, description="错误信息")
