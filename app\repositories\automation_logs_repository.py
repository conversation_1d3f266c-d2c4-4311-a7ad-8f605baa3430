"""
自动化日志Repository - 自动化任务日志数据访问层
===============================

负责自动化任务日志相关的所有数据库操作，隔离业务逻辑与数据访问技术。
支持Session注入，符合依赖注入原则。
"""

from datetime import UTC, datetime, timedelta
from typing import Any
from uuid import UUID

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.data.models.automation_logs import AutomationLogs
from app.data.models.order import Order

from .base import SQLAlchemyRepository


class AutomationLogsRepository(SQLAlchemyRepository[AutomationLogs, UUID]):
    """自动化日志Repository - 专门处理自动化任务日志数据访问"""

    def __init__(self, session: AsyncSession):
        """
        接受Session注入
        """
        super().__init__(session, AutomationLogs)

    async def get_by_application_id(self, application_id: UUID) -> list[AutomationLogs]:
        """根据申请ID获取自动化日志"""
        result = await self.session.execute(
            select(AutomationLogs)
            .where(AutomationLogs.application_id == application_id)
            .order_by(desc(AutomationLogs.created_at))
        )
        return list(result.scalars().all())

    async def get_by_order_id(self, order_id: UUID) -> list[AutomationLogs]:
        """根据订单ID获取自动化日志"""
        result = await self.session.execute(
            select(AutomationLogs)
            .where(AutomationLogs.order_id == order_id)
            .order_by(desc(AutomationLogs.created_at))
        )
        return list(result.scalars().all())

    async def get_by_celery_task_id(self, celery_task_id: str) -> AutomationLogs | None:
        """根据Celery任务ID获取自动化日志"""
        result = await self.session.execute(
            select(AutomationLogs).where(
                AutomationLogs.celery_task_id == celery_task_id
            )
        )
        return result.scalar_one_or_none()

    async def get_by_task_status(
        self,
        task_status: str,
        task_type: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[AutomationLogs]:
        """根据任务状态获取自动化日志"""
        query = select(AutomationLogs).where(AutomationLogs.task_status == task_status)

        if task_type:
            query = query.where(AutomationLogs.task_type == task_type)

        query = (
            query.order_by(desc(AutomationLogs.created_at)).limit(limit).offset(offset)
        )

        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def get_processing_tasks(
        self, task_type: str | None = None
    ) -> list[AutomationLogs]:
        """获取正在处理的任务"""
        return await self.get_by_task_status("processing", task_type)

    async def get_failed_tasks(
        self, task_type: str | None = None, hours: int = 24
    ) -> list[AutomationLogs]:
        """获取失败的任务"""
        since = datetime.now(UTC) - timedelta(hours=hours)

        query = select(AutomationLogs).where(
            and_(
                AutomationLogs.task_status == "failed",
                AutomationLogs.created_at >= since,
            )
        )

        if task_type:
            query = query.where(AutomationLogs.task_type == task_type)

        query = query.order_by(desc(AutomationLogs.created_at))

        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def update_task_status(
        self,
        log_id: UUID,
        new_status: str,
        error_message: str | None = None,
        completed_at: datetime | None = None,
    ) -> bool:
        """更新任务状态"""
        update_data: dict[str, Any] = {"task_status": new_status}

        if error_message:
            update_data["error_message"] = error_message

        if completed_at:
            update_data["completed_at"] = completed_at
        elif new_status in ["success", "failed", "cancelled"]:
            update_data["completed_at"] = datetime.now(UTC)

        result = await self.session.execute(
            update(AutomationLogs)
            .where(AutomationLogs.id == log_id)
            .values(**update_data)
        )

        await self.session.commit()
        return result.rowcount > 0

    async def update_task_by_celery_id(
        self,
        celery_task_id: str,
        new_status: str,
        error_message: str | None = None,
        completed_at: datetime | None = None,
    ) -> bool:
        """根据Celery任务ID更新任务状态"""
        update_data: dict[str, Any] = {"task_status": new_status}

        if error_message:
            update_data["error_message"] = error_message

        if completed_at:
            update_data["completed_at"] = completed_at
        elif new_status in ["success", "failed", "cancelled"]:
            update_data["completed_at"] = datetime.now(UTC)

        result = await self.session.execute(
            update(AutomationLogs)
            .where(AutomationLogs.celery_task_id == celery_task_id)
            .values(**update_data)
        )

        await self.session.commit()
        return result.rowcount > 0

    async def increment_retry_count(self, log_id: UUID) -> bool:
        """增加重试次数"""
        result = await self.session.execute(
            update(AutomationLogs)
            .where(AutomationLogs.id == log_id)
            .values(retry_count=AutomationLogs.retry_count + 1)
        )

        await self.session.commit()
        return result.rowcount > 0

    async def get_task_statistics(
        self, user_id: UUID | None = None, hours: int = 24
    ) -> dict[str, Any]:
        """获取任务统计信息"""
        since = datetime.now(UTC) - timedelta(hours=hours)

        query = select(AutomationLogs).where(AutomationLogs.created_at >= since)

        if user_id:
            query = query.join(Order, AutomationLogs.order_id == Order.id).where(
                Order.user_id == user_id
            )

        # 按状态统计
        status_result = await self.session.execute(
            select(AutomationLogs.task_status, func.count(AutomationLogs.id))
            .select_from(query.subquery())
            .group_by(AutomationLogs.task_status)
        )
        status_stats: dict[str, int] = dict(status_result.fetchall())  # type: ignore[arg-type]

        # 按任务类型统计
        type_result = await self.session.execute(
            select(AutomationLogs.task_type, func.count(AutomationLogs.id))
            .select_from(query.subquery())
            .group_by(AutomationLogs.task_type)
        )
        type_stats: dict[str, int] = dict(type_result.fetchall())  # type: ignore[arg-type]

        # 总数统计
        total_result = await self.session.execute(
            select(func.count(AutomationLogs.id)).select_from(query.subquery())
        )
        total_count = total_result.scalar()

        return {
            "total_count": total_count,
            "status_distribution": status_stats,
            "type_distribution": type_stats,
            "time_range_hours": hours,
        }

    async def get_logs_with_order_info(
        self,
        user_id: UUID | None = None,
        task_status: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """获取包含订单信息的日志列表"""
        query = select(
            AutomationLogs.id,
            AutomationLogs.task_type,
            AutomationLogs.task_status,
            AutomationLogs.error_message,
            AutomationLogs.retry_count,
            AutomationLogs.celery_task_id,
            AutomationLogs.started_at,
            AutomationLogs.completed_at,
            AutomationLogs.created_at,
            Order.order_no,
            Order.order_status,
        ).join(Order, AutomationLogs.order_id == Order.id)

        if user_id:
            query = query.where(Order.user_id == user_id)

        if task_status:
            query = query.where(AutomationLogs.task_status == task_status)

        query = (
            query.order_by(desc(AutomationLogs.created_at)).limit(limit).offset(offset)
        )

        result = await self.session.execute(query)
        rows = result.fetchall()

        return [
            {
                "id": row.id,
                "task_type": row.task_type,
                "task_status": row.task_status,
                "error_message": row.error_message,
                "retry_count": row.retry_count,
                "celery_task_id": row.celery_task_id,
                "started_at": row.started_at,
                "completed_at": row.completed_at,
                "created_at": row.created_at,
                "order_no": row.order_no,
                "order_status": row.order_status,
            }
            for row in rows
        ]
