"""add_unique_constraint_to_celery_task_id

Revision ID: 65d946459ccb
Revises: 4fc2719ff439
Create Date: 2025-07-09 14:20:52.027524

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65d946459ccb'
down_revision: Union[str, None] = '4fc2719ff439'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    print("🔧 Step 1: 删除重复的celery_task_id记录...")

    # 🔧 Step 1: 删除重复的 celery_task_id 记录
    # 保留每个 celery_task_id 的最新记录（按 created_at 排序）
    op.execute("""
        DELETE FROM automation_logs
        WHERE id NOT IN (
            SELECT DISTINCT ON (celery_task_id) id
            FROM automation_logs
            WHERE celery_task_id IS NOT NULL
            ORDER BY celery_task_id, created_at DESC
        ) AND celery_task_id IS NOT NULL;
    """)

    print("✅ 重复记录已删除")

    print("🔧 Step 2: 添加celery_task_id唯一约束...")

    # 🔧 Step 2: 添加唯一约束
    op.create_unique_constraint(
        'uq_automation_logs_celery_task_id',
        'automation_logs',
        ['celery_task_id']
    )

    print("✅ 唯一约束已添加")

    # 🔧 Step 3: 记录到migration_history表
    print("📝 记录迁移历史...")

    op.execute(f"""
        INSERT INTO migration_history (
            version_num,
            migration_name,
            description,
            tables_affected,
            applied_at,
            success
        ) VALUES (
            '{revision}',
            'add_unique_constraint_to_celery_task_id',
            '为automation_logs.celery_task_id字段添加唯一约束，防止Celery重试时产生重复记录。迁移前自动清理重复数据。',
            'automation_logs',
            now(),
            true
        )
    """)

    print("✅ 迁移历史记录完成!")


def downgrade() -> None:
    print("🔄 回滚celery_task_id唯一约束...")

    # 删除唯一约束
    op.drop_constraint(
        'uq_automation_logs_celery_task_id',
        'automation_logs',
        type_='unique'
    )

    print("✅ 唯一约束已删除")

    # 删除migration_history记录
    print("🗑️ 删除迁移历史记录...")

    op.execute(f"DELETE FROM migration_history WHERE version_num = '{revision}'")

    print("✅ 迁移历史记录已删除")
