import { useAuthStore } from '@/stores/auth'
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresGuest: true, // 只允许未登录用户访问
    },
  },
  {
    path: '/',
    name: 'Home',
    redirect: '/visa-form',
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/visa-form',
    name: 'visa-form',
    component: () => import('@/views/VisaFormView.vue'),
    meta: {
      title: '申请签证',
      requiresAuth: true,
    },
  },
  {
    path: '/order-management',
    name: 'order-management',
    component: () => import('@/views/order/OrderManagementView.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
    },
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: {
      title: '数据统计',
      requiresAuth: true,
    },
  },
  // 捕获所有未匹配的路由，重定向到登录页
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/login',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 全局前置守卫 - 身份验证
router.beforeEach(async (to, from, next) => {
  // 初始化authStore
  const authStore = useAuthStore()

  // 🔧 GitHub Copilot建议：使用路由元数据来控制认证守卫
  // 仅在需要认证的路由检查认证状态
  if (to.meta.requiresAuth) {
    // 🔧 修复：isAuthenticated内部已检查token.value，无需重复检查
    if (!authStore.isAuthenticated) {
      // 等待persist插件恢复认证状态
      // 等待authStore恢复（推荐：根据实际store实现事件或Promise）
      const restoreDelay = Number(import.meta.env.VITE_AUTH_RESTORE_DELAY) || 100
      await new Promise((resolve) => setTimeout(resolve, restoreDelay))

      if (!authStore.isAuthenticated) {
        // 跳转到登录页
        next('/login')
        return
      }
    }
  }

  // 🔧 增强：处理仅允许访客访问的路由（如登录页）
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // 已登录用户访问登录页，重定向到首页
    next('/')
    return
  }

  // 认证通过或不需要认证，允许访问
  next()
})

export default router
