<template>
  <div class="submission-status-panel">
    <!-- 简洁标题 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><Document /></el-icon>
        最近提交
      </h3>
    </div>

    <!-- 简洁统计 -->
    <div class="status-summary" v-if="applications.length > 0">
      <span class="summary-text">
        总计 {{ applicationStats.total }}人
        <span v-if="applicationStats.processing > 0" class="processing-count">
          ({{ applicationStats.processing }}人提交中)
        </span>
      </span>
    </div>

    <!-- 应用列表 -->
    <div class="applications-list">
      <div v-if="applications.length === 0" class="empty-state">
        <el-empty description="暂无提交记录" :image-size="60">
          <template #image>
            <el-icon size="60" color="#c0c4cc"><DocumentCopy /></el-icon>
          </template>
        </el-empty>
      </div>

      <div v-else class="applications-content">
        <!-- 应用项目 -->
        <div
          v-for="app in sortedApplications"
          :key="app.passportNumber"
          class="application-item"
          :class="getItemClass(app)"
        >
          <div class="applicant-info">
            <div class="applicant-name">{{ app.chineseName || app.applicantName }}</div>
          </div>

          <div class="status-section">
            <el-tag :type="getStatusTagType(app)" effect="plain" size="small">
              {{ getDisplayStatus(app).text }}
            </el-tag>

            <!-- 重试按钮 - 只在失败状态显示 -->
            <el-button
              v-if="app.automationStatus === 'failed'"
              type="warning"
              size="small"
              @click="handleRetry(app)"
              :icon="Refresh"
            >
              重试
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'

import { useApplicationStore, type ApplicationItem } from '@/stores/application'
import { Document, DocumentCopy, Refresh } from '@element-plus/icons-vue'

// 使用应用商店
const applicationStore = useApplicationStore()

// 计算属性
const applications = computed(() => applicationStore.applications)
const sortedApplications = computed(() => applicationStore.sortedApplications)
const applicationStats = computed(() => applicationStore.applicationStats)

// Emits
const emit = defineEmits<{
  viewDetails: [application: ApplicationItem]
  retry: [application: ApplicationItem]
}>()

// 🔥 重构：获取显示状态
const getDisplayStatus = (app: ApplicationItem) => {
  return applicationStore.getDisplayStatus(app)
}

// 🔧 修复：简化状态标签类型，只基于automationStatus
const getStatusTagType = (
  app: ApplicationItem,
): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    processing: 'warning',
    success: 'success',
    failed: 'danger',
    cancelled: 'info',
  }
  return typeMap[app.automationStatus] || 'info'
}

// 🔧 修复：简化项目样式类，只基于automationStatus
const getItemClass = (app: ApplicationItem) => {
  return {
    'item-processing': app.automationStatus === 'processing',
    'item-success': app.automationStatus === 'success',
    'item-failed': app.automationStatus === 'failed',
    'item-cancelled': app.automationStatus === 'cancelled',
  }
}

// 重试申请功能
const handleRetry = (application: ApplicationItem) => {
  emit('retry', application)
}

// 生命周期
onMounted(() => {
  // 🔧 修复：移除loadFromStorage调用，避免覆盖轮询更新的状态
  // applicationStore在初始化时已经自动加载localStorage数据
  console.log('📋 [状态面板] 组件已挂载，当前应用数量:', applications.value.length)
})

onUnmounted(() => {
  // 🔥 停止轮询以释放资源
  // 🔧 修复：移除调试日志
})
</script>

<style scoped lang="scss">
.submission-status-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: fit-content;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  background: #42b883;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.status-summary {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  color: #606266;

  .processing-count {
    color: #e6a23c;
    font-weight: 500;
  }
}

.applications-list {
  flex: 1;
  overflow-y: auto;

  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  .application-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s;

    &:hover {
      background: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    .applicant-info {
      flex: 1;

      .applicant-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
      }
    }

    .status-section {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-loading {
      animation: rotating 2s linear infinite;
      margin-right: 4px;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .application-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .status-section {
      justify-content: space-between;
    }
  }
}
</style>
