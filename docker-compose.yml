services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: visa_automator_postgres
    # 🔧 2025年安全最佳实践：使用环境变量而非硬编码凭据
    env_file:
      - ./.env
    environment:
      POSTGRES_DB: ${POSTGRES_DB?Variable not set}
      POSTGRES_USER: ${POSTGRES_USER?Variable not set}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD?Variable not set}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TZ: UTC
      PGTZ: UTC # PostgreSQL特定时区设置
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_connections=200
      -c timezone='UTC'
      -c log_timezone='UTC'
      -c log_line_prefix='%t [%p] %q%u@%d '
    volumes:
      - postgres_data:/var/lib/postgresql/data

    # 端口映射在开发环境的override文件中配置
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER?Variable not set} -d ${POSTGRES_DB?Variable not set}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  visa-automator:
    build: .
    # 生产环境：移除源码挂载，代码已在构建时COPY到镜像中
    env_file:
      - ./.env
    environment:
      - REDIS_HOST=redis
      - TZ=UTC
      - ENVIRONMENT=testing  # 测试环境：禁用API文档
    shm_size: 4gb
    restart: unless-stopped
    # 🔒 2025年Docker安全最佳实践：最小权限原则
    cap_drop:
      - ALL  # 移除所有Linux内核能力，防止权限提升攻击
    security_opt:
      - "no-new-privileges:true"  # 禁止容器内进程获得新权限
    # 生产环境：移除 --reload 标志
    command:
      ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
    expose:
      - "8000"
    networks:
      - app-network
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:8000/api/visa/health/', timeout=5)",
        ]
      interval: 120s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G
        reservations:
          cpus: "0.5"
          memory: 1G

  redis:
    image: redis:7-alpine
    container_name: visa_automator_redis
    command: >
      redis-server
      --save 60 1
      --loglevel warning
      --maxclients 1000
      --tcp-keepalive 60
      --tcp-backlog 511
      --timeout 300
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    # 端口映射在开发环境的override文件中配置
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 20s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # 🔥 独立邮件轮询服务
  # 专门负责邮件轮询，与Celery Worker分离，避免重复轮询
  email-polling:
    build:
      context: .
      dockerfile: Dockerfile.email
    container_name: visa_automator_email_polling
    # 生产环境：移除源码挂载
    env_file:
      - ./.env
    environment:
      - TZ=UTC
    networks:
      - app-network
    depends_on:
      visa-automator:
        condition: service_healthy
    restart: unless-stopped
    # 🔒 2025年Docker安全最佳实践：最小权限原则
    cap_drop:
      - ALL  # 移除所有Linux内核能力，防止权限提升攻击
    security_opt:
      - "no-new-privileges:true"  # 禁止容器内进程获得新权限
    # 🔥 资源限制（邮件轮询 + 签证下载需要更多资源）
    # 由于集成了Playwright浏览器自动化，需要增加资源配置
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: "2.0"
        reservations:
          memory: 1G
          cpus: "1.0"
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import requests; import sys; r = requests.get('http://localhost:8001/health', timeout=5); sys.exit(0 if r.status_code == 200 else 1)",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.celery
    #container_name: visa_automator_celery_worker  remove the name, it will be auto generated
    # 生产环境：移除源码挂载
    env_file:
      - ./.env
    environment:
      - REDIS_HOST=redis
      - TZ=UTC
    networks:
      - app-network
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    # 🔒 2025年Docker安全最佳实践：最小权限原则
    cap_drop:
      - ALL  # 移除所有Linux内核能力，防止权限提升攻击
    security_opt:
      - "no-new-privileges:true"  # 禁止容器内进程获得新权限
    healthcheck:
      test:
        ["CMD", "celery", "-A", "celery_worker.celery_app", "inspect", "active"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

  # 注意：frontend和nginx-lb服务在生产环境配置文件docker-compose.production.yml中定义
  # 开发环境使用本地前端开发服务器，不需要这些容器化服务

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
# 使用 scale 命令来启动多个 visa-automator 实例：
# docker-compose up -d --scale visa-automator=3 nginx-lb
# (这将启动 3 个 visa-automator 实例和 1 个 nginx-lb 实例)
# 或者只启动并扩展服务，Nginx 单独定义：
# docker-compose up -d --scale visa-automator=3
# docker-compose up -d nginx-lb # 需要确保 nginx-lb 依赖的 visa-automator 已启动

# 推荐的扩展配置：
# 开发环境：docker-compose up -d --scale visa-automator=2
# 生产环境：docker-compose up -d --scale visa-automator=5
# 高负载环境：docker-compose up -d --scale visa-automator=10
