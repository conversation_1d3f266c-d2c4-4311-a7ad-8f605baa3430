/**
 * 测试工具函数
 * ============
 *
 * 提供常用的测试辅助函数
 */

import { createTestingPinia } from '@pinia/testing'
import { render, type RenderOptions } from '@testing-library/vue'
import { vi } from 'vitest'
import type { Component } from 'vue'

/**
 * 渲染Vue组件用于测试
 */
export function renderComponent(component: Component, options: RenderOptions<Component> = {}) {
  return render(component, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
        }),
      ],
      stubs: {
        transition: false,
        'transition-group': false,
      },
      ...options.global,
    },
    ...options,
  })
}

/**
 * 创建测试用的表单数据
 */
export function createTestFormData() {
  return {
    surname: '<PERSON>HA<PERSON>',
    given_name: 'WEI',
    passport_number: '*********',
    date_of_birth: '1990-01-01',
    nationality: 'CHN',
    sex: 'M',
    passport_issued_date: '2020-01-01',
    passport_expiry_date: '2030-01-01',
    purpose: 'Tourism',
    visa_category: 'DL',
    visa_entry_type: 'Single',
    intended_entry_date: '2024-06-01',
    intended_exit_date: '2024-06-15',
    is_expedited: false,
    has_visited_vietnam: false,
    vietnam_contact_name: 'Hotel ABC',
    vietnam_contact_phone: '+84123456789',
    vietnam_contact_address: '123 Main St, Ho Chi Minh City',
  }
}

/**
 * 创建测试用的文件对象
 */
export function createTestFile(
  name: string = 'test.jpg',
  type: string = 'image/jpeg',
  content: string = 'test content',
) {
  return new File([content], name, { type })
}

/**
 * 等待异步操作完成
 */
export async function waitForAsync() {
  await new Promise((resolve) => setTimeout(resolve, 0))
}

/**
 * 模拟用户输入
 */
export function mockUserInput(element: HTMLElement, value: string) {
  const input = element as HTMLInputElement
  input.value = value
  input.dispatchEvent(new Event('input', { bubbles: true }))
}

/**
 * 模拟文件上传
 */
export function mockFileUpload(element: HTMLElement, files: File[]) {
  const input = element as HTMLInputElement
  Object.defineProperty(input, 'files', {
    value: files,
    writable: false,
  })
  input.dispatchEvent(new Event('change', { bubbles: true }))
}

/**
 * 创建测试用的API响应
 */
export function createApiResponse<T>(data: T, success: boolean = true) {
  return {
    success,
    data,
    message: success ? 'Success' : 'Error',
    timestamp: new Date().toISOString(),
  }
}

/**
 * 创建测试用的OCR响应
 */
export function createOcrResponse(data: Record<string, unknown> = {}) {
  return createApiResponse({
    surname: 'ZHANG',
    given_name: 'WEI',
    passport_number: '*********',
    date_of_birth: '1990-01-01',
    nationality: 'CHN',
    sex: 'M',
    passport_issued_date: '2020-01-01',
    passport_expiry_date: '2030-01-01',
    ...data,
  })
}

/**
 * 创建测试用的错误响应
 */
export function createErrorResponse(message: string = 'Test error') {
  return {
    success: false,
    data: null,
    message,
    timestamp: new Date().toISOString(),
  }
}
