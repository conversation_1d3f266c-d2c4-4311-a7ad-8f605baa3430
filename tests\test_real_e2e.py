"""
真实端到端测试
============

测试完整的签证申请流程，使用真实配置和真实网站进行验证
"""

from pathlib import Path

from playwright.sync_api import sync_playwright
import pytest

from app.core.visa_automation_engine import VisaAutomationEngine
from app.fillers.vietnam_filler import VietnamFiller


class TestRealVietnamEVisaAutomation:
    """真实的越南电子签自动化测试"""

    @pytest.fixture
    def real_engine(self):
        """使用真实配置的引擎"""
        engine = VisaAutomationEngine()

        # 验证真实配置已正确加载
        assert "vietnam_evisa" in engine.locators
        assert "vietnam_evisa_url" in engine.settings
        assert engine.settings["vietnam_evisa_url"] == "https://evisa.gov.vn/"

        return engine

    @pytest.fixture
    def real_applicant(self):
        """真实的测试申请人数据 - 使用现有的数据生成器"""
        from app.data.model import VietnamEVisaApplicant

        return VietnamEVisaApplicant(
            passport_number="E12345678",
            chinese_name="张测试",
            surname="<PERSON>",
            given_name="Test",
            dob="01/01/1990",
            nationality="China",
            sex="MALE",
            place_of_birth="Beijing",
            date_of_issue="01/01/2020",
            passport_expiry="01/01/2030",
            email="<EMAIL>",
            telephone_number="13800138000",
            contact_address="Beijing, China",
            intended_entry_gate="NHAT_TAN_BRIDGE",
            visa_start_date="01/07/2025",
            visa_validity_duration="30天",
            purpose_of_entry="Tourist",
        )

    @pytest.mark.slow
    @pytest.mark.real_website
    def test_real_website_accessibility(self, real_engine):
        """测试真实网站的可访问性"""
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()

            try:
                response = page.goto(real_engine.settings["vietnam_evisa_url"])
                assert response and response.status == 200

                # 验证页面内容
                title = page.title()
                assert "e-visa" in title.lower() or "vietnam" in title.lower()

                # 验证页面有基本的申请相关内容 - 健壮的检查
                page_content = page.content().lower()
                has_visa_content = any(
                    keyword in page_content
                    for keyword in ["visa", "apply", "application", "evisa", "e-visa"]
                )
                assert has_visa_content, "页面应包含签证申请相关内容"

            finally:
                browser.close()

    @pytest.mark.slow
    @pytest.mark.real_website
    def test_real_locators_validity(self, real_engine):
        """测试真实定位器的有效性 - 健壮版本"""
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()

            try:
                page.goto(real_engine.settings["vietnam_evisa_url"])

                # 验证页面加载完成
                page.wait_for_load_state("networkidle")

                # 检查页面是否为正常的签证申请页面
                page_content = page.content().lower()

                # 验证这是一个有效的签证申请页面
                visa_indicators = [
                    "visa",
                    "evisa",
                    "e-visa",
                    "apply",
                    "application",
                    "passport",
                    "entry",
                    "vietnam",
                ]

                found_indicators = sum(
                    1 for indicator in visa_indicators if indicator in page_content
                )
                assert found_indicators >= 3, (
                    f"页面应包含足够的签证相关指标，找到 {found_indicators} 个"
                )

                # 验证页面不是错误页面
                error_indicators = ["404", "not found", "error", "unavailable"]
                has_errors = any(error in page_content for error in error_indicators)
                assert not has_errors, "页面不应显示错误信息"

            finally:
                browser.close()

    @pytest.mark.slow
    @pytest.mark.real_website
    def test_real_vietnam_filler_integration(self, real_engine):
        """测试真实的VietnamFiller集成"""
        filler = VietnamFiller()
        filler.prepare(real_engine.locators, real_engine.settings)

        # 验证Filler配置
        assert filler.base_url == "https://evisa.gov.vn/"
        assert filler.locators == real_engine.locators["vietnam_evisa"]

        # 验证关键方法
        apply_selector = filler.get_locator("homepage.apply_now_button")
        assert apply_selector is not None
        assert apply_selector == 'button:has-text("Apply now")'

    def test_real_config_loading(self, real_engine):
        """测试真实配置文件的加载"""
        vietnam_locators = real_engine.locators["vietnam_evisa"]

        # 验证关键定位器存在
        required_sections = ["homepage", "main_form", "captcha_page"]
        for section in required_sections:
            assert section in vietnam_locators

        # 验证具体定位器
        assert "apply_now_button" in vietnam_locators["homepage"]
        assert "personal_info" in vietnam_locators["main_form"]
        assert "captcha_input" in vietnam_locators["captcha_page"]

        # 验证设置
        assert real_engine.settings["vietnam_evisa_url"] == "https://evisa.gov.vn/"
        assert "browser" in real_engine.settings

    def test_real_address_data_consistency(self):
        """测试真实地址数据的一致性"""
        filler = VietnamFiller()
        addresses = filler._get_vietnam_addresses()

        assert isinstance(addresses, dict)
        assert addresses is not None

        for address_list in addresses.values():
            assert isinstance(address_list, list)
            for address in address_list:
                # 验证地址结构
                required_fields = ["street", "province_city", "ward"]
                for field in required_fields:
                    assert field in address
                # 确认district字段已移除
                assert "district" not in address

    @pytest.mark.parametrize(
        "entry_gate",
        [
            "Noi Bai Int Airport",
            "Tan Son Nhat Int Airport (Ho Chi Minh City)",
            "Da Nang International Airport",
        ],
    )
    def test_real_entry_gate_data(self, entry_gate):
        """测试真实入境口岸数据"""
        filler = VietnamFiller()
        addresses = filler._get_vietnam_addresses()

        assert addresses is not None
        assert entry_gate in addresses
        assert len(addresses[entry_gate]) > 0

        # 验证地址格式
        first_address = addresses[entry_gate][0]
        assert all(key in first_address for key in ["street", "province_city", "ward"])


class TestRealConfigurationValidation:
    """真实配置验证测试"""

    def test_locator_file_exists(self):
        """测试定位器配置文件存在"""
        config_path = Path("config/locators/vietnam_evisa.yaml")
        assert config_path.exists()

    def test_locator_file_structure(self):
        """测试定位器文件结构正确"""
        import yaml

        config_path = Path("config/locators/vietnam_evisa.yaml")
        with open(config_path, encoding="utf-8") as f:
            locators = yaml.safe_load(f)

        # 验证主要结构
        assert isinstance(locators, dict)
        required_sections = ["homepage", "main_form", "captcha_page"]
        for section in required_sections:
            assert section in locators

        # 验证district相关配置已移除
        main_form = locators.get("main_form", {})
        vietnam_address = main_form.get("vietnam_address", {})
        assert "district_input" not in vietnam_address
        assert "district_select" not in vietnam_address

    def test_environment_variables_available(self):
        """测试必要的环境变量可用"""
        from app.utils.env_loader import load_env_var

        vietnam_url = load_env_var("VIETNAM_EVISA_URL", "")
        assert vietnam_url != ""
        assert vietnam_url and "evisa.gov.vn" in vietnam_url

        browser = load_env_var("BROWSER", "chromium")
        assert browser in ["chromium", "firefox", "webkit"]
