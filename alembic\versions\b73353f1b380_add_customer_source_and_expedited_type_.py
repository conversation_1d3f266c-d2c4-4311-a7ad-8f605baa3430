"""add customer_source and expedited_type to application table

Revision ID: b73353f1b380
Revises: c7202ff5bc32
Create Date: 2025-07-04 22:34:20.414194

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b73353f1b380'
down_revision: Union[str, None] = 'c7202ff5bc32'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """添加customer_source和expedited_type字段到application表"""
    print("🔧 Adding customer_source and expedited_type fields to application table...")

    # 添加客户来源字段
    op.add_column('application', sa.Column('customer_source', sa.String(length=128), nullable=True, comment='客户来源'))

    # 添加加急类型字段
    op.add_column('application', sa.Column('expedited_type', sa.String(length=32), nullable=True, comment='加急类型'))

    # 记录到migration_history表
    op.execute("""
        INSERT INTO migration_history (version_num, migration_name, description, tables_affected, applied_at, success)
        VALUES ('b73353f1b380', 'add_customer_source_and_expedited_type_to_application_table', '添加客户来源和加急类型字段到application表', 'application', now(), true)
    """)

    print("✅ Fields added successfully!")


def downgrade() -> None:
    """回滚：删除customer_source和expedited_type字段"""
    print("🔧 Removing customer_source and expedited_type fields from application table...")

    # 删除字段
    op.drop_column('application', 'expedited_type')
    op.drop_column('application', 'customer_source')

    # 删除migration_history记录
    op.execute("DELETE FROM migration_history WHERE version_num = 'b73353f1b380'")

    print("✅ Fields removed successfully!")
