"""
Payment Automation Module
-------------------------
负责自动化越南电子签证的最终信用卡付款流程
支持多卡配置和随机付款功能

依赖:
- playwright: 浏览器自动化
- random: 随机卡选择
- json: 配置文件读取
- os.path: 文件路径处理
"""

from pathlib import Path
import re
import secrets
import time
from typing import Any

from dotenv import load_dotenv
from playwright.sync_api import Page, TimeoutError

from app.payment.payment_models import CreditCardInfo
from app.utils.logger_config import get_logger

# 加载环境变量
load_dotenv()

logger = get_logger()


# 自定义异常类
class FormValidationError(Exception):
    """表单验证错误异常"""

    pass


class PaymentButtonDisabled(Exception):
    """支付按钮禁用状态异常"""

    pass


class AgreementDialogError(Exception):
    """协议对话框处理异常"""

    pass


def load_credit_cards() -> list[CreditCardInfo]:
    """
    加载信用卡配置，仅从环境变量加载

    Returns:
        List[CreditCardInfo]: 有效信用卡列表（card_number不为空）
    """
    # 从环境变量加载
    from app.utils.env_loader import load_credit_cards_from_env

    env_cards_data = load_credit_cards_from_env()

    if not env_cards_data:
        logger.error(
            "❌ 环境变量中没有信用卡配置，请在.env文件中添加CREDIT_CARD_相关配置"
        )
        return []

    logger.info(f"✅ 从环境变量加载了 {len(env_cards_data)} 张信用卡")
    cards = []
    for card_dict in env_cards_data:
        # 严格按照JSON文件中的字段顺序创建CreditCardInfo对象
        card = CreditCardInfo(
            card_type=card_dict.get("card_type", ""),
            card_number=card_dict.get("card_number", ""),
            first_name=card_dict.get("first_name", ""),
            last_name=card_dict.get("last_name", ""),
            billing_address=card_dict.get("billing_address", ""),
            city=card_dict.get("city", ""),
            country=card_dict.get("country", ""),
            exp_month=card_dict.get("exp_month", ""),
            exp_year=card_dict.get("exp_year", ""),
            cvv=card_dict.get("cvv", ""),
            note=card_dict.get("note", ""),
        )
        cards.append(card)
    return cards


def force_click_with_retry(page: Page, selector: str, retries=3, delay=500) -> bool:
    """暴力点击重试（平衡简洁性与健壮性）"""

    for attempt in range(retries):
        try:
            page.locator(selector).click(timeout=3000, force=True)
            logger.info(
                f"✅ force_click_with_retry暴力点击Continue成功 (第{attempt + 1}次)"
            )
            return True
        except Exception as e:
            if attempt < retries - 1:
                logger.warning(
                    f"⚡ force_click_with_retry暴力点击重试中...（错误：{str(e)}）",
                    exc_info=True,
                )
                page.wait_for_timeout(delay)
    logger.error(
        f"❌ force_click_with_retry暴力点击失败（共 {retries} 次尝试）", exc_info=True
    )
    return False


def wait_for_i_agree(page, timeout=5000):
    """I agree按钮"""
    # page.wait_for_selector('.modal, .dialog, .ant-modal, .bootstrap-dialog', timeout=timeout)
    page.wait_for_selector('button:has-text("I agree")', timeout=timeout)


def smart_click(
    page,
    selector,
    fast_mode=True,  # 默认开启快速暴力模式
    retries=3,
    fallback_to_force=True,
    post_click_check=wait_for_i_agree,  # ⬅️ 默认启用检测 I agree 按钮
    retry_delay=100,
):
    """智能点击：优先快速模式，支持监听响应，点击后验证，自动重试3次"""

    for attempt in range(1, retries + 1):
        try:
            page.wait_for_timeout(100)  # 每次点击前都等待0.1秒 ✅

            logger.info(f"🔄 尝试第 {attempt}/{retries} 次点击 {selector} ...")
            if attempt > 1:
                page.wait_for_timeout(retry_delay)  # 每次失败后等待

            if fast_mode:
                # 快速模式：直接暴力点击
                page.locator(selector).click(timeout=1000, force=True)
            else:
                # 标准模式：完整检测后点击
                page.locator(selector).click(timeout=1000)

            # ⬇️ 点击后检查 I agree按钮
            if post_click_check:
                try:
                    post_click_check(page)
                    logger.info(
                        f"✅ smart_click第 {attempt} 次点击后成功检测到 I agree 按钮"
                    )
                    return True  # 检查成功就结束
                except Exception as e:
                    logger.warning(
                        f"⚠️ smart_click第 {attempt} 次点击后未检测到I agree按钮: {e}"
                    )
                    return False

        except Exception:
            if attempt == retries - 1:
                if fallback_to_force and not fast_mode:
                    # 最终回退到暴力点击
                    return force_click_with_retry(page, selector)
                return False
            logger.info(f"⚠️ 暴力点击 {selector} 失败，尝试切换模式...")

            fast_mode = not fast_mode  # 切换模式
    return False


# 截图函数
def _take_screenshot(page: Page, dir: Path, name: str):
    """拍摄页面截图并保存"""
    try:
        path = dir / f"{name}_{int(time.time())}.png"
        page.screenshot(path=str(path))  # 确保传递字符串路径
        logger.info(f"📸 截图已保存至: {path}")  # 改为info级别确保显示
    except Exception as e:
        logger.warning(f"⚠️ 无法截取屏幕: {e}")


# --- 重构后的支付结果验证函数 ---


def _verify_payment_result(page: Page, screenshots_dir: Path) -> bool:
    """
    通过健壮的多层验证策略（URL + 页面内容）确认最终支付结果。
    该函数优雅、高效，并遵循 Playwright 最佳实践。
    """
    logger.info("🔄 开始多层次验证支付结果...")

    # ===== 定义关键词 (单一事实来源) =====
    # 将所有可能的成功/失败词汇定义在这里，易于维护
    SUCCESS_KEYWORDS = [
        "success",
        "successful",
        "succeeded",
        "successfully",
        "successed",
    ]
    FAILURE_KEYWORDS = [
        "failure",
        "failed",
        "decline",
        "error",
        "declined",
        "unsuccessful",
        "unsuccessfully",
    ]

    # 构建正则表达式，'|'表示或, 'i'表示不区分大小写
    # re.compile() 可以提高重复使用的效率
    SUCCESS_REGEX = re.compile("|".join(SUCCESS_KEYWORDS), re.IGNORECASE)
    FAILURE_REGEX = re.compile("|".join(FAILURE_KEYWORDS), re.IGNORECASE)

    # ===== 阶段 1: 等待并验证 URL 导航 =====
    try:
        logger.info("  📍 阶段1: 等待页面导航至付款结果URL...")

        # 将成功和失败的关键词合并，用于等待任一结果的URL
        all_result_keywords = SUCCESS_KEYWORDS + FAILURE_KEYWORDS
        url_regex = re.compile("|".join(all_result_keywords), re.IGNORECASE)

        # 等待URL变化，超时60秒
        page.wait_for_url(url_regex, timeout=60000)

        current_url = page.url
        logger.info(f"  📍 当前URL: {current_url}")

        # 检查最终的URL属于哪个类别
        if FAILURE_REGEX.search(current_url):
            logger.info(f" ❌阶段1失败: 页面已导航至付款失败URL -> {current_url}")
            return False

        # 检查是否匹配成功模式
        if not SUCCESS_REGEX.search(current_url):  # type: ignore[unreachable]
            # URL既不匹配成功也不匹配失败模式（理论上不应该发生，但为了健壮性）
            logger.info(f"⚠️ 阶段1付款状态不确定,请查看截图确认: 页面URL: {current_url}")
            _take_screenshot(page, screenshots_dir, "payment_uncertain_at_url")
            return False

        # 如果URL验证成功，继续进行阶段2验证
        logger.info(
            f"✅ 阶段1付款状态确认成功,继续进行阶段2的付款页面状态检测，当前页面已导航至成功付款URL: {current_url}"
        )

    except TimeoutError:
        logger.error("❌ 阶段1超时: 在60秒内未导航到任何预期的成功或失败页面")
        _take_screenshot(page, screenshots_dir, "payment_timeout_on_result")
        return False
    except Exception as e:
        logger.error(f"❌ 阶段1异常: {e}", exc_info=True)
        _take_screenshot(page, screenshots_dir, "payment_stage1_error")
        return False

    # ===== 阶段 2: 验证页面可见文本 =====
    try:  # type: ignore[unreachable]
        logger.info("  📝 阶段2: 正在验证页面上的最终状态文本...")
        # 创建一个组合定位器，等待成功或失败的文本出现
        result_locator = (
            page.get_by_text(SUCCESS_REGEX).or_(page.get_by_text(FAILURE_REGEX)).first
        )

        # 等待该元素在页面上可见
        result_locator.wait_for(state="visible", timeout=30000)

        # 获取文本内容
        found_text = result_locator.text_content()

        # 再次用正则表达式检查找到的文本
        if found_text and SUCCESS_REGEX.search(found_text):
            logger.info(
                f" ✅ 支付结果检测确认：支付成功！在当前页面上找到成功文本: '{found_text}'"
            )
            _take_screenshot(page, screenshots_dir, "payment_successful")
            # =新增的安全等待块，等待关键支付步骤完成， 避免提前关闭浏览器影响支付结果，遵守网站"请勿关闭浏览器"提示
            try:
                logger.info(
                    "  ⏳ 验证支付结果为：支付成功!现在开始等待，让网页完成关键的支付步骤处理"
                )
                # 等待网络空闲，给予30秒的宽裕时间
                page.wait_for_load_state("networkidle", timeout=30000)
                logger.info(" ✅ 页面网络已稳定，网站后台支付步骤处理很可能已完成。")
            except TimeoutError:
                # 即使超时，我们也认为支付是成功的，因为关键成功支付文本已经出现。
                # 这只是一个警告，表示某些后台脚本可能仍在运行。
                logger.warning(
                    "  ⚠️ 在安全等待期间网络活动未完全停止(超时)，但支付结果已被确认为成功。忽略警告，继续执行"
                )
            return True

        elif found_text and FAILURE_REGEX.search(found_text):
            logger.error(f"❌ 最终确认失败: 在页面上找到失败文本: '{found_text}'")
            _take_screenshot(page, screenshots_dir, "payment_failed_on_page")
            return False
        elif found_text:
            # 找到了文本但既不匹配成功也不匹配失败模式（不应该发生但为了健壮性）
            logger.warning(
                f"⚠️ 找到无法确认付款状态的文本: '{found_text}'，按失败处理，请检查截图"
            )
            _take_screenshot(page, screenshots_dir, "payment_unexpected_text")
            return False
        else:
            logger.error("❌ 无法获取页面文本内容，请查看截图")
            _take_screenshot(page, screenshots_dir, "payment_no_text_content")
            return False

    except TimeoutError:
        logger.warning(
            "⚠️ 阶段2超时: 导航到了成功URL，但在页面上未找到明确的成功/失败文本"
        )
        _take_screenshot(page, screenshots_dir, "payment_result_uncertain")
        return False  # 安全原则：不确定的状态 = 失败
    except Exception as e:
        logger.error(f"❌ 阶段2异常: {e}", exc_info=True)
        _take_screenshot(page, screenshots_dir, "payment_stage2_error")
        return False


def fill_payment_form(page, card, logger):
    """
    健壮的表单填写函数，遵循Playwright最佳实践，并在填写后截图。

    Args:
        page: Playwright页面对象
        card: CreditCardInfo对象
        logger: 日志对象
        screenshots_dir: 截图目录

    Returns:
        bool: 表单填写是否成功
    """
    logger.info("🔄 开始填写支付表单...")

    try:
        # ===== 1. 填写标准文本字段 (数据驱动) =====
        # 将字段定义为数据结构，易于维护。
        # .fill() 会自动等待元素可见并可编辑，无需手动wait_for_selector。
        standard_fields = {
            "#card_number": card.card_number,
            "#card_first_name": card.first_name,
            "#card_last_name": card.last_name,
            "#billing_address": card.billing_address,
            "#city": card.city,
            "#cvv2": card.cvv,
        }

        for selector, value in standard_fields.items():
            # 这里设置一个合理的超时，比如10秒，适用于所有字段填写。
            page.locator(selector).fill(value, timeout=10000)

        logger.info("  - ✅ 已填写所有文本字段。")

        # ===== 2. 处理自定义下拉框 (国家选择) - 防御性验证版 =====
        # 移除所有 wait_for_timeout，用条件等待代替。
        page.locator("#select2-country-container").click()

        search_box = page.locator(".select2-search__field")
        # 直接对搜索框进行操作，Playwright会自动等待它出现
        search_box.fill(card.country)
        # 输入后按回车，这是一个原子操作，通常无需等待
        search_box.press("Enter")

        # 防御性验证：确认选择操作真正生效
        country_container = page.locator("#select2-country-container")
        from playwright.sync_api import expect

        expect(country_container).to_have_text(
            re.compile(card.country, re.IGNORECASE), timeout=5000
        )
        logger.info(f"  - ✅ 已确认选择国家: {card.country}")

        # ===== 3. 处理标准下拉框 (过期日期) - 简化版 =====
        # 移除手动重试循环，信任Playwright的内置等待。
        page.locator("#expMonth").select_option(card.exp_month)
        page.locator("#expYear").select_option(card.exp_year)
        logger.info(f"  - ✅ 已选择过期日期:{card.exp_month}/{card.exp_year}")

        # ===== 4. 最终截图确认 (核心需求) =====
        logger.info("✅ 付款表单填写完成！")
        # 生成模式不截图，仅供调试使用
        # _take_screenshot(page, screenshots_dir, "form_filled_confirmation")

        return True

    except TimeoutError as e:
        # 如果任何一步超时（字段未找到、无法填写等），都会被这里捕获。
        logger.error("❌ 填写表单失败：在某一步骤超时。请检查截图。")
        logger.error(f"  - 错误详情: {e}")
        # _take_screenshot(page, screenshots_dir, "form_fill_failed")  # 截图功能已注释
        return False
    except Exception as e:
        # 捕获其他意外错误
        logger.error(f"❌ 填写表单时发生意外错误: {e}", exc_info=True)
        # _take_screenshot(page, screenshots_dir, "form_fill_error")  # 截图功能已注释
        return False


# 原来的process_payment_with_existing_page函数，现在改名为process_payment
def process_payment(
    page, payment_url: str | None = None, settings: dict[str, Any] | None = None
) -> bool:
    """
    使用已存在的页面处理支付流程，无需重新启动浏览器

    Args:
        page: 已存在的Playwright页面对象
        payment_url: 已废弃参数，保留仅为兼容性
        settings: 可选的配置参数

    Returns:
        bool: 支付是否成功
    """
    settings = settings or {}

    # 设置超时参数(默认300秒)
    # timeout = settings.get("payment_timeout_ms", 300000)  # 未使用，已注释

    # 设置截图目录
    screenshots_dir = Path(settings.get("screenshots_dir", "payment_screenshots"))
    screenshots_dir.mkdir(exist_ok=True)

    # 加载信用卡列表
    cards = load_credit_cards()
    if not cards:
        logger.error("❌ 没有有效的信用卡信息，无法完成支付")
        return False

    # 随机选择一张卡
    card = secrets.choice(cards)
    card_number = card.card_number
    masked_number = f"****{card_number[-4:]}" if len(card_number) >= 4 else "****"
    logger.info(f"✅ 随机选择信用卡: {card.card_type} {masked_number}")
    if card.note:
        logger.info(f"✅ 卡片备注: {card.note}")

    try:
        # 无缝模式：直接使用当前页面，不再导航
        logger.info(f"✅ 当前页面URL: {page.url}")
        logger.info("🔄 开始处理付款流程...")

        # 截图记录当前页面状态
        page.screenshot(
            path=str(screenshots_dir / f"payment_start_{int(time.time())}.png")
        )

        logger.info("🔄 尝试点击支付区域...")

        try:
            page.get_by_text("PAY BY VISA / MASTERCARD / JCB / AMEX").click()
            logger.info("✅ 已点击支付区域")

        except Exception as e:
            logger.warning(f"⚠️ 支付区域点击失败，尝试继续流程: {e}")

        # 2. 选择卡类型 - 使用纯语义化定位器
        logger.info(f"🔄 选择卡类型: {card.card_type}")

        try:
            card_type = card.card_type.upper()

            # 使用语义化定位器，利用Playwright的自动等待和重试机制
            card_link_locator = page.get_by_title(f"PAY BY {card_type}").or_(
                page.locator(f"a[href*='{card_type}']")
            )

            # 直接点击，让Playwright处理等待逻辑
            card_link_locator.click()
            logger.info(f"✅ 已选择卡类型: {card_type}")

        except Exception as e:
            logger.warning(f"❌ 卡类型选择失败: {e}")

        # 3. 使用优化的函数填写支付表单
        if not fill_payment_form(page, card, logger):
            logger.error("❌ 填写支付表单失败")
            page.screenshot(
                path=str(screenshots_dir / f"form_fill_failed_{int(time.time())}.png")
            )
            return False

        # 4. 提交支付
        logger.info("✅ 信用卡信息填写完成，提交最后支付流程...")

        try:
            # ===== 第一阶段：处理Continue按钮 =====
            if smart_click(page, "button#pay-button"):
                logger.info("✅ 已确认点击Continue按键成功")
            else:
                logger.error("❌ 确认点击Continue按键失败")
                page.screenshot(
                    path=str(
                        screenshots_dir
                        / f"continue_click_failed_{int(time.time())}.png"
                    )
                )
                # 尝试其他按钮
                alternative_buttons = [
                    'button:has-text("Continue")',
                    'button:has-text("Pay")',
                    'button:has-text("Submit")',
                    'input[type="submit"]',
                ]
                for btn in alternative_buttons:
                    try:
                        if page.locator(btn).is_visible(timeout=2000):
                            page.locator(btn).click(force=True)
                            logger.info(f"尝试点击替代按钮: {btn}")
                            break
                    except Exception:
                        continue  # nosec B112 # 继续尝试下一个按钮

            # ===== 第二阶段：处理I agree按钮 =====
            try:
                i_agree_button = page.locator("#btn-confirm")
                logger.info("✅ 检测到 I agree 按钮，正在等待并点击I agree按钮...")
                i_agree_button.click(timeout=10000)
                logger.info("✅ 已成功点击 I agree 按钮。")
                # 等待I agree按钮消失
                i_agree_button.wait_for(state="hidden", timeout=5000)
                logger.info(
                    f"✅ I agree 按钮已从 DOM 中移除，确认点击成功，当前页面URL是: {page.url}"
                )
                _take_screenshot(page, screenshots_dir, "after_i_agree_button")

            except TimeoutError:
                logger.warning(
                    f"⚠️ 未能在规定时间内处理 I agree 按钮或者页面加载出错，当前页面URL是: {page.url}，忽略并继续"
                )
            except Exception as e:
                logger.warning(f"⚠️ 处理 I agree 按钮时出错: {e}")

            # ===== 第三阶段：第二次点击Continue按键 最终确认 =====
            try:
                final_continue = page.locator('button#pay-button:has-text("Continue")')
                final_continue.click(timeout=120000)  # 设置120秒超时
                logger.info("✅ 第二次点击 Continue 按钮成功")
            except Exception as e:
                logger.warning(f"⚠️ 第二次点击 Continue 失败，但继续执行: {e}")

            # ===== 支付结果验证 =====
            return _verify_payment_result(page, screenshots_dir)

        except Exception as e:
            page.screenshot(path=str(screenshots_dir / f"error_{int(time.time())}.png"))
            logger.error(f"❌ 支付流程异常: {e}", exc_info=True)
            return False
    except Exception as e:
        logger.error(f"❌ 支付过程出错: {e}")
        return False
