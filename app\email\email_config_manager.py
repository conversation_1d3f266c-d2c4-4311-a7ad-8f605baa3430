"""
邮件配置管理器
==============

统一管理邮件轮询相关的所有配置，消除重复的配置加载逻辑

功能：
- 邮箱账户配置管理
- 时间窗口配置
- 重试策略配置
- 缓存管理
- 配置验证和热重载
"""

from dataclasses import dataclass, field
from datetime import UTC, datetime
import json
import os
from pathlib import Path
from zoneinfo import ZoneInfo

from app.utils.env_loader import load_email_accounts_from_env
from app.utils.logger_config import get_logger

logger = get_logger()


@dataclass
class EmailAccountConfig:
    """邮箱账户配置"""

    email: str
    host: str
    port: int
    password: str
    allowed_senders: list[str] = field(default_factory=list)
    enabled: bool = True

    def __post_init__(self):
        """配置验证"""
        if not self.email or "@" not in self.email:
            raise ValueError(f"无效的邮箱地址: {self.email}")
        if not self.host:
            raise ValueError(f"邮箱主机不能为空: {self.email}")
        if not (1 <= self.port <= 65535):
            raise ValueError(f"无效的端口号: {self.port}")
        if not self.password:
            raise ValueError(f"邮箱密码不能为空: {self.email}")


@dataclass
class PollingConfig:
    """轮询配置"""

    # 活动时间窗口（小时）
    active_windows: list[tuple[int, int]] = field(
        default_factory=lambda: [(9, 13), (16, 23)]
    )

    # 重试配置
    max_retries: int = 3
    retry_backoff: float = 2.0
    retry_delay_base: int = 5

    # 线程池配置
    max_workers: int = 10
    thread_name_prefix: str = "email_worker"

    # 调度器配置
    timezone: str = "UTC"
    misfire_grace_time: int = 60
    coalesce: bool = True
    max_instances: int = 1

    # 轮询间隔配置
    min_poll_interval: int = 30  # 最小轮询间隔（分钟）
    max_poll_interval: int = 59  # 最大轮询间隔（分钟）

    def __post_init__(self):
        """配置验证"""
        for start, end in self.active_windows:
            if not (0 <= start < 24 and 0 <= end <= 24):
                raise ValueError(f"无效的时间窗口: {start}-{end}")
            if start >= end:
                raise ValueError(f"时间窗口开始时间必须小于结束时间: {start}-{end}")


@dataclass
class CacheConfig:
    """缓存配置"""

    uid_cache_file: str = "uid_cache.json"
    auto_save: bool = True
    backup_count: int = 3


class EmailConfigManager:
    """邮件配置管理器"""

    def __init__(self, config_file: str | None = None):
        self.config_file = config_file
        self.accounts: dict[str, EmailAccountConfig] = {}
        self.polling_config = PollingConfig()
        self.cache_config = CacheConfig()
        self.uid_cache: dict[str, int] = {}
        self._last_reload_time: datetime | None = None

        # 初始化配置
        self._load_all_configs()

    def _load_all_configs(self):
        """加载所有配置"""
        try:
            self._load_email_accounts()
            self._load_polling_config()
            self._load_uid_cache()
            self._last_reload_time = datetime.now(UTC)
            logger.info("✅ 邮件配置加载完成")

        except Exception as e:
            logger.error(f"❌ 加载邮件配置失败: {e}", exc_info=True)
            raise

    def _load_email_accounts(self):
        """加载邮箱账户配置"""
        try:
            logger.info("🔍 加载邮箱账户配置...")

            # 从环境变量加载
            email_config = load_email_accounts_from_env()
            email_accounts_map = email_config.get("email_accounts", {})
            allowed_senders_map = email_config.get("allowed_senders", {})

            self.accounts.clear()

            for email_addr, config_dict in email_accounts_map.items():
                if not isinstance(config_dict, dict):
                    logger.warning(f"⚠️ 跳过无效的邮箱配置: {email_addr}")
                    continue

                try:
                    account_config = EmailAccountConfig(
                        email=email_addr,
                        host=config_dict.get("host", ""),
                        port=int(config_dict.get("port", 993)),
                        password=config_dict.get("password", ""),
                        allowed_senders=allowed_senders_map.get(email_addr, []),
                        enabled=config_dict.get("enabled", True),
                    )

                    self.accounts[email_addr] = account_config
                    logger.info(f"✅ 加载邮箱账户: {email_addr}")

                except ValueError as e:
                    logger.error(f"❌ 邮箱配置验证失败 [{email_addr}]: {e}")
                    continue

            logger.info(f"📊 总共加载 {len(self.accounts)} 个邮箱账户")

        except Exception as e:
            logger.error(f"❌ 加载邮箱账户配置失败: {e}")
            raise

    def _load_polling_config(self):
        """加载轮询配置"""
        try:
            # 从环境变量或配置文件加载轮询配置
            # 这里可以扩展为从配置文件读取

            # 从环境变量读取配置
            max_workers = int(os.getenv("EMAIL_MAX_WORKERS", "10"))
            max_retries = int(os.getenv("EMAIL_MAX_RETRIES", "3"))
            timezone = os.getenv("EMAIL_TIMEZONE", "UTC")

            self.polling_config = PollingConfig(
                max_workers=max_workers, max_retries=max_retries, timezone=timezone
            )

            logger.debug(
                f"✅ 轮询配置加载完成: {max_workers} workers, {max_retries} retries"
            )

        except Exception as e:
            logger.warning(f"⚠️ 加载轮询配置失败，使用默认值: {e}")
            self.polling_config = PollingConfig()

    def _load_uid_cache(self):
        """加载UID缓存"""
        try:
            cache_file = Path(self.cache_config.uid_cache_file)
            if cache_file.exists():
                with open(cache_file, encoding="utf-8") as f:
                    self.uid_cache = json.load(f)
                logger.debug(f"✅ 加载UID缓存: {len(self.uid_cache)} 个账户")
            else:
                self.uid_cache = {}
                logger.debug("📝 创建新的UID缓存")

        except Exception as e:
            logger.warning(f"⚠️ 加载UID缓存失败: {e}")
            self.uid_cache = {}

    def save_uid_cache(self):
        """保存UID缓存"""
        try:
            cache_file = Path(self.cache_config.uid_cache_file)

            # 备份现有缓存
            if cache_file.exists() and self.cache_config.backup_count > 0:
                self._backup_cache_file(cache_file)

            # 保存新缓存
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(self.uid_cache, f, indent=2, ensure_ascii=False)
            logger.debug("✅ UID缓存已保存")

        except Exception as e:
            logger.warning(f"⚠️ 保存UID缓存失败: {e}")

    def _backup_cache_file(self, cache_file: Path):
        """备份缓存文件"""
        try:
            timestamp = datetime.now(UTC).strftime("%Y%m%d_%H%M%S")
            backup_file = cache_file.with_suffix(f".{timestamp}.bak")

            # 复制文件
            import shutil

            shutil.copy2(cache_file, backup_file)

            # 清理旧备份
            self._cleanup_old_backups(cache_file.parent, cache_file.stem)

        except Exception as e:
            logger.warning(f"⚠️ 备份缓存文件失败: {e}")

    def _cleanup_old_backups(self, backup_dir: Path, file_stem: str):
        """清理旧备份文件"""
        try:
            backup_pattern = f"{file_stem}.*.bak"
            backup_files = list(backup_dir.glob(backup_pattern))

            # 按修改时间排序，保留最新的几个
            backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

            for old_backup in backup_files[self.cache_config.backup_count :]:
                old_backup.unlink()
                logger.debug(f"🗑️ 删除旧备份: {old_backup.name}")

        except Exception as e:
            logger.warning(f"⚠️ 清理旧备份失败: {e}")

    def get_account_config(self, email: str) -> EmailAccountConfig | None:
        """获取指定邮箱的配置"""
        return self.accounts.get(email)

    def get_enabled_accounts(self) -> dict[str, EmailAccountConfig]:
        """获取所有启用的邮箱配置"""
        return {
            email: config for email, config in self.accounts.items() if config.enabled
        }

    def update_uid_cache(self, email: str, uid: int):
        """更新UID缓存"""
        self.uid_cache[email] = uid
        if self.cache_config.auto_save:
            self.save_uid_cache()

    def get_last_uid(self, email: str) -> int:
        """获取指定邮箱的最后处理UID"""
        return self.uid_cache.get(email, 0)

    def is_within_active_window(self) -> bool:
        """检查当前时间是否在活动窗口内"""
        now = datetime.now(UTC).time()
        for start, end in self.polling_config.active_windows:
            if start <= now.hour < end:
                return True
        return False

    def get_timezone(self) -> ZoneInfo:
        """获取时区对象"""
        return ZoneInfo(self.polling_config.timezone)

    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            logger.info("🔄 重新加载邮件配置...")
            self._load_all_configs()
            return True
        except Exception as e:
            logger.error(f"❌ 重新加载配置失败: {e}")
            return False

    def validate_config(self) -> list[str]:
        """验证配置并返回错误列表"""
        errors = []

        # 验证邮箱账户
        if not self.accounts:
            errors.append("没有配置任何邮箱账户")

        for email, config in self.accounts.items():
            if not config.allowed_senders:
                errors.append(f"邮箱 {email} 没有配置允许的发件人")

        # 验证轮询配置
        if self.polling_config.max_workers <= 0:
            errors.append("线程池大小必须大于0")

        if self.polling_config.max_retries < 0:
            errors.append("最大重试次数不能为负数")

        return errors

    def get_config_summary(self) -> dict:
        """获取配置摘要"""
        return {
            "accounts_count": len(self.accounts),
            "enabled_accounts": len(self.get_enabled_accounts()),
            "active_windows": self.polling_config.active_windows,
            "max_workers": self.polling_config.max_workers,
            "timezone": self.polling_config.timezone,
            "last_reload": self._last_reload_time.isoformat()
            if self._last_reload_time
            else None,
            "cache_entries": len(self.uid_cache),
        }
