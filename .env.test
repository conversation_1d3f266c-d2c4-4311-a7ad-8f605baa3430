# 测试环境配置文件
# 复制自 .env 并根据测试环境需求调整

# 数据库配置
POSTGRES_DB=visa_automator_test
POSTGRES_USER=visa_user_test
POSTGRES_PASSWORD=test_password_2024_change_this

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379

# 应用配置
SECRET_KEY=test_secret_key_change_this
DEBUG=false
ENVIRONMENT=testing

# API配置
API_V1_STR=/api
BACKEND_CORS_ORIGINS=["http://localhost:8081"]

# 邮件配置（测试环境可以使用假的SMTP）
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=<EMAIL>
SMTP_PASSWORD=test_password
EMAILS_FROM_EMAIL=<EMAIL>

# 安全配置
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# 时区配置
TZ=UTC

# 测试环境特定配置
DISABLE_API_DOCS=true  # 禁用API文档
LOG_LEVEL=INFO

# 注意：API_BASIC_USERS已移除，项目使用FastAPI Users进行JWT认证
