"""
真实数据库集成测试 - 简化版本
============================

专注于测试真实的数据库操作和业务逻辑，避免过度Mock
使用实际存在的方法和状态，提高代码覆盖率
"""

from datetime import date
import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.order_repository import OrderRepository
from app.services.order_service import OrderService
from backend.api.schemas.order import CreateOrderRequest, OrderQueryParams
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestRealDatabaseIntegration:
    """真实数据库集成测试"""

    @pytest.fixture
    async def real_order_service(self, db_session: AsyncSession):
        """创建使用真实数据库的OrderService"""
        order_repo = OrderRepository(db_session)
        service = OrderService(order_repo)
        await service.initialize()
        return service

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """创建测试用户"""
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_order_creation_flow(
        self, real_order_service: OrderService, test_user
    ):
        """测试完整的订单创建流程"""
        request = CreateOrderRequest(
            applicant_name="张三",
            passport_number="E12345678",
            date_of_birth=date(1990, 1, 1),
            application_data={
                "applicant_name": "张三",
                "passport_number": "E12345678",
                "visa_type": "tourist",
            },
        )

        # 执行真实的业务逻辑
        result = await real_order_service.create_order(test_user.id, request)

        # 验证结果
        assert result["success"] is True
        assert "order_no" in result["data"]
        assert result["data"]["is_duplicate"] is False

        # 验证数据库中的数据
        order_no = result["data"]["order_no"]
        detail_result = await real_order_service.get_order_detail(
            test_user.id, order_no
        )

        assert detail_result["success"] is True
        assert detail_result["data"]["order"]["order_no"] == order_no
        assert detail_result["data"]["order"]["status"] == "created"

    @pytest.mark.asyncio
    async def test_duplicate_detection(
        self, real_order_service: OrderService, test_user
    ):
        """测试重复订单检测"""
        request = CreateOrderRequest(
            applicant_name="李四",
            passport_number="E87654321",
            date_of_birth=date(1985, 5, 15),
            application_data={
                "applicant_name": "李四",
                "passport_number": "E87654321",
            },
        )

        # 第一次创建
        result1 = await real_order_service.create_order(test_user.id, request)
        assert result1["success"] is True
        assert result1["data"]["is_duplicate"] is False

        # 第二次创建相同订单
        result2 = await real_order_service.create_order(test_user.id, request)
        assert result2["success"] is True
        # 注意：根据实际业务逻辑，可能允许重复创建或检测重复

    @pytest.mark.asyncio
    async def test_order_status_update(
        self, real_order_service: OrderService, test_user
    ):
        """测试订单状态更新"""
        # 创建订单
        request = CreateOrderRequest(
            applicant_name="王五",
            passport_number="E11111111",
            date_of_birth=date(1992, 3, 20),
            application_data={"applicant_name": "王五"},
        )

        result = await real_order_service.create_order(test_user.id, request)
        order_no = result["data"]["order_no"]

        # 更新状态
        update_result = await real_order_service.update_order_status(
            user_id=test_user.id,
            order_no=order_no,
            new_status=OrderStatus.CANCELLED,
            reason="测试取消",
        )

        assert update_result["success"] is True

        # 验证状态已更新
        detail_result = await real_order_service.get_order_detail(
            test_user.id, order_no
        )
        assert detail_result["data"]["order"]["status"] == "cancelled"

    @pytest.mark.asyncio
    async def test_order_query_pagination(
        self, real_order_service: OrderService, test_user
    ):
        """测试订单查询和分页"""
        # 创建多个订单
        for i in range(3):
            request = CreateOrderRequest(
                applicant_name=f"用户{i}",
                passport_number=f"E{str(i).zfill(8)}",
                date_of_birth=date(1990, 1, 1),
                application_data={},
            )
            await real_order_service.create_order(test_user.id, request)

        # 测试查询
        query_params = OrderQueryParams(page=1, limit=2, status=None)
        result = await real_order_service.query_user_orders(test_user.id, query_params)

        assert result["success"] is True
        assert "data" in result

    @pytest.mark.asyncio
    async def test_order_statistics(self, real_order_service: OrderService, test_user):
        """测试订单统计"""
        # 创建订单
        request = CreateOrderRequest(
            applicant_name="统计测试",
            passport_number="E99999999",
            date_of_birth=date(1990, 1, 1),
            application_data={},
        )
        await real_order_service.create_order(test_user.id, request)

        # 获取统计
        stats = await real_order_service.get_order_stats(test_user.id)
        assert isinstance(stats, dict)

    @pytest.mark.asyncio
    async def test_repository_operations(self, db_session: AsyncSession, test_user):
        """测试Repository层的直接操作"""
        order_repo = OrderRepository(db_session)

        # 创建订单
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": "created",
            }
        )
        created_order = await order_repo.create(order)
        assert created_order.id is not None

        # 按用户查询
        user_orders = await order_repo.get_by_user_id(test_user.id)
        assert len(user_orders) >= 1

        # 按订单号查询
        found_order = await order_repo.get_by_order_no(
            created_order.order_no, test_user.id
        )
        assert found_order is not None
        assert found_order.id == created_order.id

        # 测试统计
        stats = await order_repo.get_order_stats(test_user.id)
        assert "total_orders" in stats
        assert stats["total_orders"] >= 1

    @pytest.mark.asyncio
    async def test_error_handling(self, real_order_service: OrderService):
        """测试错误处理"""
        # 测试不存在的订单
        fake_user_id = uuid.uuid4()
        detail_result = await real_order_service.get_order_detail(
            fake_user_id, "VN20250101NOTEXIST"
        )
        assert detail_result["success"] is False

    @pytest.mark.asyncio
    async def test_concurrent_operations(
        self, real_order_service: OrderService, test_user
    ):
        """测试并发操作"""
        # 简化并发测试，避免复杂的并发问题
        request1 = CreateOrderRequest(
            applicant_name="并发用户1",
            passport_number="*********",
            date_of_birth=date(1990, 1, 1),
            application_data={},
        )
        result1 = await real_order_service.create_order(test_user.id, request1)
        assert result1["success"] is True

        request2 = CreateOrderRequest(
            applicant_name="并发用户2",
            passport_number="E00000002",
            date_of_birth=date(1990, 1, 1),
            application_data={},
        )
        result2 = await real_order_service.create_order(test_user.id, request2)
        assert result2["success"] is True

        # 验证订单号唯一性
        order_no1 = result1["data"]["order_no"]
        order_no2 = result2["data"]["order_no"]
        assert order_no1 != order_no2


class TestRepositoryDirectAccess:
    """直接测试Repository层"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_repository_crud_operations(
        self, order_repo: OrderRepository, test_user
    ):
        """测试Repository的CRUD操作"""
        # Create
        order = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_status": "created",
            }
        )
        created_order = await order_repo.create(order)
        assert created_order.id is not None

        # Read
        found_order = await order_repo.get_by_id(created_order.id)
        assert found_order is not None
        assert found_order.order_no == created_order.order_no

        # Update
        found_order.order_status = "cancelled"
        updated_order = await order_repo.update(found_order)
        assert updated_order.order_status == "cancelled"

        # Delete (soft delete if implemented)
        deleted = await order_repo.delete(created_order.id)
        assert deleted is True

    @pytest.mark.asyncio
    async def test_repository_complex_queries(
        self, order_repo: OrderRepository, test_user
    ):
        """测试Repository的复杂查询"""
        # 创建多个订单
        for i in range(3):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": "created",
                    "order_no": f"VN20250711TEST{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试分页查询
        page1 = await order_repo.get_by_user_id(test_user.id, limit=2, offset=0)
        assert len(page1) == 2

        page2 = await order_repo.get_by_user_id(test_user.id, limit=2, offset=2)
        assert len(page2) == 1

        # 测试按状态查询
        created_orders = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders) >= 3
