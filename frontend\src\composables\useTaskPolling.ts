/**
 * 订单轮询组合式函数 - 新架构版本
 * 🔥 适配新架构：通过FastAPI查询automation_logs获取任务状态
 */

import { computed, onMounted, onUnmounted, readonly, ref } from 'vue'

import { api } from '@/api/request'
import type { OrderInfo } from '@/api/types'
import { useApplicationStore } from '@/stores/application'
import { useAuthStore } from '@/stores/auth'
import { redirectToLogin } from '@/utils/navigation'
import { getCurrentUTCTimestamp } from '@/utils/timeFormat'

interface PollingOptions {
  interval?: number
  enabled?: boolean
}

interface TaskStatus {
  order_no: string
  status: 'processing' | 'success' | 'failed' | 'cancelled' | 'pending'
  message?: string
  task_type?: string
  celery_task_id?: string
  started_at?: string
  completed_at?: string
  updated_at: string
}

// 状态更新记录接口
interface StatusUpdateRecord {
  orderNo: string
  oldStatus: string
  newStatus: string
  timestamp: string
  message: string
}

// 轮询错误类型
interface PollingError {
  message?: string
  toString?: () => string
}

interface HttpError {
  response?: {
    status: number
  }
  message?: string
}

// 定义API响应类型 - 🔧 修复：API直接返回{success, data}格式
interface ApiResponse<T> {
  success: boolean
  data: T
}

export function useTaskPolling(options: PollingOptions = {}) {
  const { interval = 30000, enabled = true } = options

  // 状态管理
  const isPolling = ref(false)
  const isConnected = ref(true)
  const isPageVisible = ref(true)
  const hasError = ref(false)
  const errorMessage = ref('')
  const canRetry = ref(true)

  // 订单和状态更新
  const orders = ref<OrderInfo[]>([])
  const statusUpdates = ref<StatusUpdateRecord[]>([])

  // 应用商店
  const applicationStore = useApplicationStore()
  // 🎯 使用认证store替代直接localStorage访问
  const authStore = useAuthStore()

  let pollingTimer: number | null = null

  /**
   * 查询单个任务的状态
   */
  const queryTaskStatus = async (orderNo: string): Promise<TaskStatus | null> => {
    try {
      // ✅ 依赖统一拦截器处理认证，移除手动token检查

      // 🔧 临时调试日志
      console.log(`🔍 [轮询] 查询订单状态: ${orderNo}`)

      // 🔧 修复：api.get由于响应拦截器已经返回解包后的数据
      const apiResponse = await api.get<ApiResponse<TaskStatus>>(
        `/api/automation-logs/status/${orderNo}`,
      )

      // 🔧 临时调试：显示完整响应
      console.log(`📡 [轮询] API响应 ${orderNo}:`, JSON.stringify(apiResponse, null, 2))

      // 🔧 修复：apiResponse就是{success, data}格式
      if (apiResponse?.success) {
        const taskStatus = apiResponse.data
        console.log(`✅ [轮询] 解析任务状态 ${orderNo}:`, taskStatus)
        return taskStatus
      } else {
        console.warn(`❌ [轮询] API响应不成功 ${orderNo}:`, apiResponse)
        return null
      }
    } catch (error: unknown) {
      // 🔧 修复：具体的错误处理，不再静默忽略
      console.warn(`⚠️ [轮询] 查询任务状态失败 ${orderNo}:`, error)

      // 检查404/401错误（权限验证失败或订单不存在）
      if (
        typeof error === 'object' &&
        error !== null &&
        'response' in error &&
        ((error as HttpError).response?.status === 404 ||
          (error as HttpError).response?.status === 401)
      ) {
        // 🔒 安全修复：404/401都意味着无权限访问，应该从轮询列表移除
        const removed = applicationStore.removeApplicationByOrderNo(orderNo)
        if (removed) {
          console.warn(
            `🔒 [轮询] 安全防护：订单 ${orderNo} 无权限访问(${(error as HttpError).response?.status})，已从应用列表移除`,
          )
        }
      }

      return null
    }
  }

  /**
   * 执行轮询
   */
  const performPolling = async () => {
    if (!enabled || !isPageVisible.value) {
      return
    }

    try {
      hasError.value = false
      errorMessage.value = ''

      // 🔥 实现真正的轮询逻辑：查询automation_logs状态
      const applications = applicationStore.applications

      // 🔧 临时调试：显示当前应用列表
      console.log(
        '🔄 [轮询] 开始轮询，当前应用列表:',
        applications.map((app) => ({
          orderNo: app.orderNo,
          automationStatus: app.automationStatus,
          name: app.chineseName ?? app.applicantName,
        })),
      )

      // 🔥 修复：轮询所有处理中的任务，即使orderNo为undefined
      const processingApps = applications.filter((app) => app.automationStatus === 'processing')

      console.log(`🎯 [轮询] 找到${processingApps.length}个处理中的任务`)

      if (processingApps.length === 0) {
        console.log('✅ [轮询] 没有处理中的任务，跳过轮询')
        isConnected.value = true
        return
      }

      // 批量查询任务状态
      for (const app of processingApps) {
        try {
          // 🔥 修复：如果orderNo为undefined，跳过查询
          if (!app.orderNo) {
            console.warn('⚠️ [轮询] 跳过无订单号的应用:', app.passportNumber)
            continue
          }

          const taskStatus = await queryTaskStatus(app.orderNo)

          if (taskStatus) {
            console.log(
              `🔄 [轮询] 状态比较 ${app.orderNo}: 本地=${app.automationStatus}, 服务器=${taskStatus.status}`,
            )

            if (taskStatus.status !== app.automationStatus) {
              // 只更新有效的状态（排除pending状态）
              if (taskStatus.status !== 'pending') {
                console.log(
                  `🔄 [轮询] 状态更新 ${app.orderNo}: ${app.automationStatus} -> ${taskStatus.status}`,
                )

                // 更新自动化任务状态
                applicationStore.updateApplicationStatusByOrderNo(
                  app.orderNo,
                  taskStatus.status as 'processing' | 'success' | 'failed' | 'cancelled',
                )

                // 记录状态更新
                statusUpdates.value.unshift({
                  orderNo: app.orderNo,
                  oldStatus: app.automationStatus,
                  newStatus: taskStatus.status,
                  timestamp: getCurrentUTCTimestamp(),
                  message: taskStatus.message ?? '自动化任务状态已更新',
                })

                // 限制状态更新记录数量
                if (statusUpdates.value.length > 50) {
                  statusUpdates.value = statusUpdates.value.slice(0, 50)
                }

                console.log(`✅ [轮询] 状态更新完成 ${app.orderNo}`)
              } else {
                console.log(`⏳ [轮询] 跳过pending状态 ${app.orderNo}`)
              }
            } else {
              console.log(`✅ [轮询] 状态未变化 ${app.orderNo}: ${taskStatus.status}`)
            }
          } else {
            console.warn(`❌ [轮询] 无法获取任务状态 ${app.orderNo}`)
          }
        } catch (error: unknown) {
          // 🔧 修复：详细的错误处理，不再静默忽略
          console.warn(`⚠️ [轮询] 轮询订单 ${app.orderNo} 失败:`, error)

          // 检查是否为404/401错误（权限验证失败或订单不存在）
          if (
            typeof error === 'object' &&
            error !== null &&
            'response' in error &&
            ((error as HttpError).response?.status === 404 ||
              (error as HttpError).response?.status === 401)
          ) {
            // 🔧 安全修复：404/401都意味着无权限访问，应该从轮询列表移除，而不是标记为失败
            // 这是跨用户数据泄露的防护措施
            const removed = applicationStore.removeApplicationByOrderNo(app.orderNo!)
            if (removed) {
              console.warn(
                `🔒 [轮询] 安全防护：订单 ${app.orderNo} 无权限访问(${(error as HttpError).response?.status})，已从轮询列表移除`,
              )
            }
          }
          // 对于其他错误，继续重试（下次轮询时再试）
        }
      }

      isConnected.value = true
      console.log('🔄 [轮询] 本轮轮询完成')
    } catch (error: unknown) {
      let pollingError: PollingError = {}

      // 安全的错误消息提取
      if (typeof error === 'object' && error !== null && 'message' in error) {
        pollingError = error as PollingError
      } else if (typeof error === 'string') {
        pollingError.message = error
      } else if (error instanceof Error) {
        pollingError.message = error.message
      } else {
        // 对于其他类型的错误，使用默认消息
        pollingError.message = '轮询失败'
      }

      hasError.value = true
      errorMessage.value = pollingError.message ?? '轮询失败'
      isConnected.value = false

      console.error('💥 [轮询] 轮询过程出错:', pollingError)
    }
  }

  /**
   * 开始轮询
   */
  const startPolling = () => {
    if (!enabled) {
      return
    }

    // ✅ 移除手动认证检查，依赖统一拦截器处理
    isPolling.value = true

    // 立即执行一次
    performPolling()

    // 设置定时器
    if (pollingTimer) {
      clearInterval(pollingTimer)
    }

    pollingTimer = window.setInterval(performPolling, interval)
  }

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    isPolling.value = false

    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    hasError.value = false
    errorMessage.value = ''
    canRetry.value = true
  }

  /**
   * 获取最近的状态更新
   */
  const getRecentUpdates = (limit = 10) => {
    return statusUpdates.value.slice(0, limit)
  }

  /**
   * 页面可见性处理
   */
  const handleVisibilityChange = () => {
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && isPolling.value) {
      // 页面重新可见时，立即执行一次轮询
      performPolling()
    }
  }

  /**
   * 监听认证状态变化
   */
  const watchAuthStatus = () => {
    // ✅ 使用authStore状态，移除直接localStorage操作
    const checkAuthStatus = () => {
      // 🔧 修复：检查token而不是完整的认证状态，避免误判
      const hasToken = !!authStore.token

      // 只有在确实没有token时才认为用户登出
      if (!hasToken && isPolling.value) {
        stopPolling()
        hasError.value = true
        errorMessage.value = '用户已登出'

        // 🔧 修复：自动跳转到登录页，而不是只显示错误消息
        redirectToLogin()
      } else if (hasToken && !isPolling.value && enabled) {
        clearError()
        // 延迟启动，避免频繁切换
        setTimeout(() => {
          if (enabled && !isPolling.value) {
            startPolling()
          }
        }, 1000)
      }
    }

    // 定期检查认证状态（每30秒）
    const authCheckInterval = setInterval(checkAuthStatus, 30000)

    return () => clearInterval(authCheckInterval)
  }

  // 生命周期管理
  onMounted(() => {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 🔐 启动认证状态监听
    const stopAuthWatch = watchAuthStatus()

    // ✅ 简化启动逻辑，依赖统一拦截器处理认证
    if (enabled) {
      startPolling()
    }

    // 清理函数
    onUnmounted(() => {
      stopPolling()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      stopAuthWatch()
    })
  })

  // 计算属性
  const activeTasksCount = computed(() => {
    return applicationStore.applications.filter((app) => app.automationStatus === 'processing')
      .length
  })

  const completedTasksCount = computed(() => {
    return applicationStore.applications.filter(
      (app) =>
        app.automationStatus === 'success' ||
        app.automationStatus === 'failed' ||
        app.automationStatus === 'cancelled',
    ).length
  })

  // 🔧 调试函数：手动测试轮询
  const debugPolling = async () => {
    await performPolling()
  }

  return {
    // 状态
    isPolling: readonly(isPolling),
    isConnected: readonly(isConnected),
    isPageVisible: readonly(isPageVisible),
    hasError: readonly(hasError),
    errorMessage: readonly(errorMessage),
    canRetry: readonly(canRetry),

    // 数据
    orders: readonly(orders),
    statusUpdates: readonly(statusUpdates),

    // 计算属性
    activeTasksCount,
    completedTasksCount,

    // 方法
    startPolling,
    stopPolling,
    performPolling,
    clearError,
    getRecentUpdates,
    queryTaskStatus,
    debugPolling, // 🔧 调试方法
  }
}
