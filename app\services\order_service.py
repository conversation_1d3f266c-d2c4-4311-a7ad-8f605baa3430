import asyncio
from datetime import UTC, date, datetime
import secrets
import string
from typing import Any
import uuid
from uuid import UUID

from app.utils.logger_config import logger
from backend.api.schemas.order import CreateOrderRequest, OrderQueryParams
from backend.models.order import OrderStatus


def make_json_serializable(data: Any) -> Any:
    """递归地将数据中的非JSON序列化类型转换为可序列化类型"""
    if isinstance(data, uuid.UUID):
        return str(data)
    elif isinstance(data, datetime | date):
        return data.isoformat()
    elif isinstance(data, dict):
        return {key: make_json_serializable(value) for key, value in data.items()}
    elif isinstance(data, list | tuple):
        return [make_json_serializable(item) for item in data]
    elif hasattr(data, "__dict__"):
        return {
            key: make_json_serializable(value)
            for key, value in data.__dict__.items()
            if not key.startswith("_")
        }
    else:
        return data


class OrderNumberGenerator:
    """订单编号生成器 - 确保唯一性和不可推算性"""

    @staticmethod
    def generate_order_number(date_obj: datetime | None = None) -> str:
        """生成订单编号：VN + 8位日期 + 6位随机码"""
        if date_obj is None:
            # 🔧 修复：使用UTC时区，与数据库时区保持一致
            date_obj = datetime.now(UTC)

        date_part = date_obj.strftime("%Y%m%d")
        chars = string.ascii_uppercase + string.digits
        random_part = "".join(secrets.choice(chars) for _ in range(6))

        return f"VN{date_part}{random_part}"

    @staticmethod
    def validate_order_number(order_no: str) -> bool:
        """验证订单编号格式"""
        import re

        pattern = r"^VN\d{8}[A-Z0-9]{6}$"
        return bool(re.match(pattern, order_no))


class OrderService:
    """订单管理核心服务 - Repository模式"""

    def __init__(self, order_repository):
        """依赖注入：Repository由外部传入"""
        self.order_repository = order_repository

    async def initialize(self):
        """服务初始化"""
        logger.info("OrderService已初始化")

    async def create_order(
        self, user_id: UUID, request: CreateOrderRequest
    ) -> dict[str, Any]:
        """创建订单 - 核心方法"""
        import time

        request_timestamp = int(time.time() * 1000000)
        technical_key = (
            f"{user_id}_{request_timestamp}_{hash(str(request.application_data))}"
        )

        try:
            # 检查重复订单
            existing_order = await self.order_repository.check_duplicate_order(
                user_id, technical_key, within_seconds=1
            )

            if existing_order:
                return {
                    "success": True,
                    "data": {
                        "order_no": existing_order.order_no,
                        "status": existing_order.order_status,
                        "created_at": existing_order.created_at.isoformat(),
                        "is_duplicate": True,
                    },
                    "message": "检测到重复请求，返回已创建的订单",
                }

            # 生成唯一订单编号
            max_retries = 5
            order_no = None

            for attempt in range(max_retries):
                try:
                    temp_order_no = OrderNumberGenerator.generate_order_number()
                    existing = await self.order_repository.get_by_order_no(
                        temp_order_no
                    )
                    if existing is None:
                        order_no = temp_order_no
                        break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise Exception(
                            f"订单编号生成失败，已重试{max_retries}次: {str(e)}"
                        )
                await asyncio.sleep(0.1)

            if not order_no:
                raise Exception("无法生成唯一的订单编号")

            # 创建新订单记录
            from app.data.models.order import Order

            new_order = Order(
                order_no=order_no,
                idempotent_key=technical_key,
                user_id=user_id,
                order_status="created",
                order_type="visa_application",
            )

            await self.order_repository.create(new_order)

            return {
                "success": True,
                "data": {
                    "order_no": order_no,
                    "status": new_order.order_status,
                    "created_at": new_order.created_at.isoformat(),
                    "is_duplicate": False,
                },
                "message": "订单创建成功",
            }

        except Exception as e:
            logger.error(f"订单创建失败: {str(e)}")
            raise Exception(f"订单创建失败: {str(e)}")

    async def get_order_detail(self, user_id: UUID, order_no: str) -> dict[str, Any]:
        """获取订单详情"""
        try:
            order = await self.order_repository.get_by_order_no(order_no)

            if not order or order.user_id != user_id:
                return {"success": False, "message": "订单不存在"}

            return {
                "success": True,
                "data": {
                    "order": {
                        "order_no": order.order_no,
                        "status": order.order_status,
                        "user_id": str(order.user_id),
                        "order_type": order.order_type,
                        "created_at": order.created_at.isoformat(),
                        "updated_at": order.updated_at.isoformat(),
                        "notes": order.notes or "",
                        "idempotent_key": order.idempotent_key or "",
                    },
                    "status_history": [],
                },
                "message": "获取订单详情成功",
            }

        except Exception as e:
            logger.error(f"获取订单详情失败: {str(e)}")
            return {"success": False, "message": f"获取订单详情失败: {str(e)}"}

    async def query_user_orders(
        self, user_id: UUID, params: OrderQueryParams
    ) -> dict[str, Any]:
        """查询用户订单列表"""
        try:
            return await self.order_repository.query_user_orders(user_id, params)
        except Exception as e:
            return {"success": False, "message": f"查询订单失败: {str(e)}"}

    async def update_order_status(
        self,
        user_id: UUID,
        order_no: str,
        new_status: OrderStatus,
        reason: str | None = None,
        operator_id: int | None = None,
        operator_type: str = "user",
    ) -> dict[str, Any]:
        """更新订单状态"""
        try:
            return await self.order_repository.update_order_status(
                user_id=user_id,
                order_no=order_no,
                new_status=new_status,
                reason=reason,
                operator_id=operator_id,
                operator_type=operator_type,
            )
        except Exception as e:
            return {"success": False, "message": f"更新订单状态失败: {str(e)}"}

    async def close(self):
        """关闭服务（Repository模式中由外部管理连接）"""
        pass

    async def get_order_stats(self, user_id: UUID) -> dict[str, Any]:
        """获取用户订单统计信息"""
        try:
            return await self.order_repository.get_order_stats(user_id)
        except Exception as e:
            return {"success": False, "message": f"获取统计信息失败: {str(e)}"}
