"""fix_timezone_defaults_to_utc_v2

Revision ID: 8c9231189790
Revises: adffeee83004
Create Date: 2025-07-04 09:35:33.488608

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8c9231189790'
down_revision: Union[str, None] = 'adffeee83004'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    更新所有时间字段的DEFAULT约束从Asia/Shanghai时区改为UTC。
    我们不删除表，只修改DEFAULT约束，确保新记录使用UTC时间。
    现有数据保持不变，因为它们已经有时区信息。

    根据数据库查询结果，总共需要修复23个时间字段（26行中减去3个date类型字段）。
    """
    # 更新 applicant 表的时间字段默认值
    op.alter_column('applicant', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('applicant', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 application 表的时间字段默认值
    op.alter_column('application', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('application', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 automation_logs 表的时间字段默认值 - 修复：包含所有4个字段
    op.alter_column('automation_logs', 'started_at',
                   server_default=sa.text('now()'))
    op.alter_column('automation_logs', 'completed_at',
                   server_default=sa.text('now()'))
    op.alter_column('automation_logs', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('automation_logs', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 file 表的时间字段默认值
    op.alter_column('file', 'uploaded_at',
                   server_default=sa.text('now()'))
    op.alter_column('file', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('file', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 order 表的时间字段默认值
    op.alter_column('order', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('order', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 user 表的时间字段默认值
    op.alter_column('user', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('user', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 user_payment 表的时间字段默认值 - 修复：包含所有3个字段
    op.alter_column('user_payment', 'paid_at',
                   server_default=sa.text('now()'))
    op.alter_column('user_payment', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('user_payment', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 visa_payment 表的时间字段默认值 - 修复：包含所有3个字段
    op.alter_column('visa_payment', 'paid_at',
                   server_default=sa.text('now()'))
    op.alter_column('visa_payment', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('visa_payment', 'updated_at',
                   server_default=sa.text('now()'))

    # 更新 visa_status_history 表的时间字段默认值
    op.alter_column('visa_status_history', 'created_at',
                   server_default=sa.text('now()'))
    op.alter_column('visa_status_history', 'updated_at',
                   server_default=sa.text('now()'))


def downgrade() -> None:
    """
    回滚操作：将所有时间字段的DEFAULT约束改回Asia/Shanghai时区。
    确保可以安全回滚到原来的状态。

    总共回滚23个时间字段（26行中减去3个date类型字段）。
    """
    # 回滚 applicant 表的时间字段默认值
    op.alter_column('applicant', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('applicant', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 application 表的时间字段默认值
    op.alter_column('application', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('application', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 automation_logs 表的时间字段默认值 - 修复：包含所有4个字段
    op.alter_column('automation_logs', 'started_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('automation_logs', 'completed_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('automation_logs', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('automation_logs', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 file 表的时间字段默认值
    op.alter_column('file', 'uploaded_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('file', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('file', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 order 表的时间字段默认值
    op.alter_column('order', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('order', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 user 表的时间字段默认值
    op.alter_column('user', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('user', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 user_payment 表的时间字段默认值 - 修复：包含所有3个字段
    op.alter_column('user_payment', 'paid_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('user_payment', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('user_payment', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 visa_payment 表的时间字段默认值 - 修复：包含所有3个字段
    op.alter_column('visa_payment', 'paid_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('visa_payment', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('visa_payment', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    # 回滚 visa_status_history 表的时间字段默认值
    op.alter_column('visa_status_history', 'created_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
    op.alter_column('visa_status_history', 'updated_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))
