"""
Redis会话管理测试套件
===================

测试单设备登录会话管理的各种场景，包括：
- JWT配置一致性
- Token生成和验证
- Redis连接和降级策略
- 会话存储和验证逻辑
- 错误处理和恢复机制
"""

import asyncio
from datetime import UTC, datetime
import os
from unittest.mock import AsyncMock, patch
import uuid

import jwt
import pytest

from backend.utils.single_device_session import SingleDeviceSessionManager

# ===========================
# 测试配置和标记
# ===========================

pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.session_management,
]


# ===========================
# 测试数据容器 (遵循最佳实践)
# ===========================


class SessionTestContainer:
    """会话测试数据容器 - 参考pytest最佳实践"""

    def __init__(self, user_id: str, token: str, jti: str, secret: str):
        self.user_id = user_id
        self.token = token
        self.jti = jti
        self.secret = secret


# ===========================
# 核心Fixtures
# ===========================


@pytest.fixture
def test_secret():
    """测试用JWT secret"""
    return "test-secret-key-for-session-tests"


@pytest.fixture
def test_user_id():
    """测试用户ID"""
    return str(uuid.uuid4())


@pytest.fixture
def jwt_payload_factory():
    """JWT payload工厂函数"""

    def _create_payload(user_id: str, jti: str | None = None) -> dict:
        now = int(datetime.now(UTC).timestamp())
        return {
            "sub": user_id,
            "aud": ["fastapi-users:auth"],
            "iat": now,
            "exp": now + 3600,
            "jti": jti or str(uuid.uuid4()),
        }

    return _create_payload


@pytest.fixture
def session_container(test_secret, test_user_id, jwt_payload_factory):
    """会话测试容器 - 包含完整的测试数据"""
    jti = str(uuid.uuid4())
    payload = jwt_payload_factory(test_user_id, jti)
    token = jwt.encode(payload, test_secret, algorithm="HS256")

    return SessionTestContainer(
        user_id=test_user_id, token=token, jti=jti, secret=test_secret
    )


@pytest.fixture
async def mock_redis():
    """Mock Redis客户端"""
    mock_client = AsyncMock()
    mock_client.ping.return_value = True
    mock_client.setex.return_value = True
    mock_client.get.return_value = None
    mock_client.delete.return_value = True
    mock_client.expire.return_value = True
    return mock_client


@pytest.fixture
def session_manager_with_mock_redis(mock_redis, test_secret):
    """使用Mock Redis的会话管理器"""
    with patch.dict(os.environ, {"SECRET_KEY": test_secret}):
        manager = SingleDeviceSessionManager()
        manager.redis_client = mock_redis
        return manager


# ===========================
# JWT配置和Token测试
# ===========================


class TestJWTConfiguration:
    """JWT配置一致性测试"""

    def test_secret_key_consistency(self, test_secret):
        """测试JWT Secret配置一致性"""
        with patch.dict(os.environ, {"SECRET_KEY": test_secret}):
            # 模拟settings.py的配置
            settings_secret = os.getenv("SECRET_KEY", "your-very-secret-key")

            # 模拟SingleDeviceSessionManager的配置
            manager = SingleDeviceSessionManager()
            session_secret = manager.jwt_secret

            assert settings_secret == session_secret
            assert session_secret == test_secret

    @pytest.mark.parametrize(
        "secret_value,expected",
        [
            ("custom-secret-123", "custom-secret-123"),
            (None, "your-very-secret-key"),  # 默认值
        ],
    )
    def test_secret_key_fallback(self, secret_value, expected):
        """参数化测试SECRET_KEY回退逻辑"""
        with patch.dict(os.environ, {}, clear=True):  # 清空环境变量
            if secret_value is not None:
                os.environ["SECRET_KEY"] = secret_value

            manager = SingleDeviceSessionManager()
            assert manager.jwt_secret == expected


class TestJWTTokenOperations:
    """JWT Token操作测试"""

    def test_token_generation_with_jti(
        self, test_secret, test_user_id, jwt_payload_factory
    ):
        """测试带JTI的Token生成"""
        jti = str(uuid.uuid4())
        payload = jwt_payload_factory(test_user_id, jti)

        token = jwt.encode(payload, test_secret, algorithm="HS256")

        # 验证token格式
        assert isinstance(token, str)
        assert len(token.split(".")) == 3  # JWT三段式结构

        # 验证解码
        decoded = jwt.decode(
            token, test_secret, algorithms=["HS256"], audience=["fastapi-users:auth"]
        )

        assert decoded["sub"] == test_user_id
        assert decoded["jti"] == jti
        assert decoded["aud"] == ["fastapi-users:auth"]

    def test_jti_extraction_success(
        self, session_container, session_manager_with_mock_redis
    ):
        """测试成功提取JTI"""
        extracted_jti = session_manager_with_mock_redis._extract_jti_from_token(
            session_container.token
        )

        assert extracted_jti == session_container.jti

    @pytest.mark.parametrize(
        "invalid_token",
        [
            "invalid.token.here",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
            "",
            None,
        ],
    )
    def test_jti_extraction_failure(
        self, invalid_token, session_manager_with_mock_redis
    ):
        """参数化测试无效Token的JTI提取"""
        # 所有无效输入都应该返回None（内部异常处理）
        extracted_jti = session_manager_with_mock_redis._extract_jti_from_token(
            invalid_token
        )
        assert extracted_jti is None


# ===========================
# Redis连接和降级策略测试
# ===========================


class TestRedisConnectionAndFallback:
    """Redis连接和降级策略测试"""

    async def test_redis_connection_success(
        self, session_manager_with_mock_redis, mock_redis
    ):
        """测试Redis连接成功"""
        mock_redis.ping.return_value = True

        # 通过validate_user_token间接测试ping
        mock_redis.get.return_value = "test-jti"

        with patch.object(
            session_manager_with_mock_redis,
            "_extract_jti_from_token",
            return_value="test-jti",
        ):
            result = await session_manager_with_mock_redis.validate_user_token(
                "user123", "token"
            )

        mock_redis.ping.assert_called_once()
        assert result is True

    async def test_redis_connection_failure_fallback(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试Redis连接失败时的降级策略"""
        # 模拟Redis连接失败
        mock_redis.ping.side_effect = Exception("Redis connection failed")

        result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )

        # 降级策略：如果token有效则返回True
        assert result is True
        mock_redis.ping.assert_called_once()

    async def test_redis_unavailable_with_invalid_token(
        self, session_manager_with_mock_redis, mock_redis
    ):
        """测试Redis不可用且Token无效时的处理"""
        mock_redis.ping.side_effect = Exception("Redis connection failed")

        result = await session_manager_with_mock_redis.validate_user_token(
            "user123", "invalid-token"
        )

        # Token无效，即使降级也应返回False
        assert result is False


# ===========================
# 会话存储和验证测试
# ===========================


class TestSessionStorageAndValidation:
    """会话存储和验证测试"""

    async def test_store_user_token_success(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试成功存储用户Token"""
        mock_redis.setex.return_value = True

        result = await session_manager_with_mock_redis.store_user_token(
            session_container.user_id, session_container.token
        )

        assert result is True
        mock_redis.setex.assert_called_once_with(
            f"single_device_session:{session_container.user_id}",
            7200,  # session_expire
            session_container.jti,
        )

    async def test_store_user_token_failure(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试存储Token失败"""
        mock_redis.setex.side_effect = Exception("Redis error")

        result = await session_manager_with_mock_redis.store_user_token(
            session_container.user_id, session_container.token
        )

        assert result is False

    async def test_validate_existing_session(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试验证现有会话"""
        # 模拟Redis中存在匹配的JTI
        mock_redis.get.return_value = session_container.jti
        mock_redis.expire.return_value = True

        result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )

        assert result is True
        # 验证会话刷新
        mock_redis.expire.assert_called_once()

    async def test_validate_mismatched_session(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试验证不匹配的会话"""
        # 模拟Redis中存在不同的JTI
        mock_redis.get.return_value = "different-jti"

        result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )

        assert result is False

    async def test_validate_missing_session_auto_restore(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试缺失会话的自动恢复"""
        # 模拟Redis中没有会话
        mock_redis.get.return_value = None
        mock_redis.setex.return_value = True

        result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )

        assert result is True
        # 验证自动重新存储
        mock_redis.setex.assert_called_once()

    async def test_remove_user_session(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试删除用户会话"""
        await session_manager_with_mock_redis.remove_user_token(
            session_container.user_id
        )

        mock_redis.delete.assert_called_once_with(
            f"single_device_session:{session_container.user_id}"
        )


# ===========================
# 集成测试
# ===========================


@pytest.mark.integration
class TestSessionManagerIntegration:
    """会话管理器集成测试"""

    async def test_full_session_lifecycle(
        self, session_manager_with_mock_redis, mock_redis, session_container
    ):
        """测试完整的会话生命周期"""
        # 1. 存储会话
        mock_redis.setex.return_value = True
        store_result = await session_manager_with_mock_redis.store_user_token(
            session_container.user_id, session_container.token
        )
        assert store_result is True

        # 2. 验证会话
        mock_redis.get.return_value = session_container.jti
        mock_redis.expire.return_value = True
        validate_result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )
        assert validate_result is True

        # 3. 删除会话
        await session_manager_with_mock_redis.remove_user_token(
            session_container.user_id
        )
        mock_redis.delete.assert_called_once()

    @pytest.mark.parametrize(
        "redis_error,expected_fallback",
        [
            (Exception("Connection timeout"), True),
            (ConnectionError("Redis unreachable"), True),
            (Exception("Unknown error"), True),
        ],
    )
    async def test_error_handling_scenarios(
        self,
        session_manager_with_mock_redis,
        mock_redis,
        session_container,
        redis_error,
        expected_fallback,
    ):
        """参数化测试各种错误场景的处理"""
        mock_redis.ping.side_effect = redis_error

        result = await session_manager_with_mock_redis.validate_user_token(
            session_container.user_id, session_container.token
        )

        # 所有Redis错误都应触发降级策略
        assert result == expected_fallback


# ===========================
# 性能和边界测试
# ===========================


@pytest.mark.performance
class TestSessionPerformanceAndBoundaries:
    """会话管理性能和边界测试"""

    async def test_concurrent_session_operations(
        self,
        session_manager_with_mock_redis,
        mock_redis,
        test_secret,
        jwt_payload_factory,
    ):
        """测试并发会话操作"""
        # 模拟多个用户同时操作
        mock_redis.setex.return_value = True

        user_ids = [str(uuid.uuid4()) for _ in range(10)]

        # 为每个用户生成有效的JWT token
        tokens = []
        for user_id in user_ids:
            jti = str(uuid.uuid4())
            payload = jwt_payload_factory(user_id, jti)
            token = jwt.encode(payload, test_secret, algorithm="HS256")
            tokens.append(token)

        tasks = [
            session_manager_with_mock_redis.store_user_token(user_id, token)
            for user_id, token in zip(user_ids, tokens, strict=False)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 所有操作都应该成功
        assert all(result is True for result in results)

    @pytest.mark.parametrize("jti_length", [8, 36, 64, 128])
    async def test_various_jti_lengths(
        self,
        session_manager_with_mock_redis,
        mock_redis,
        test_secret,
        test_user_id,
        jti_length,
    ):
        """测试不同长度的JTI处理"""
        jti = "a" * jti_length
        now = int(datetime.now(UTC).timestamp())

        payload = {
            "sub": test_user_id,
            "aud": ["fastapi-users:auth"],
            "iat": now,
            "exp": now + 3600,
            "jti": jti,
        }

        token = jwt.encode(payload, test_secret, algorithm="HS256")

        extracted_jti = session_manager_with_mock_redis._extract_jti_from_token(token)
        assert extracted_jti == jti
        assert len(extracted_jti) == jti_length


# ===========================
# 测试配置
# ===========================


@pytest.fixture(autouse=True)
def cleanup_environment():
    """自动清理测试环境"""
    # 保存测试前的环境变量状态
    test_keys = ["SECRET_KEY", "REDIS_HOST", "JWT_SECRET"]
    original_values = {key: os.environ.get(key) for key in test_keys}

    yield

    # 测试后恢复原始环境变量
    for key, value in original_values.items():
        if value is not None:
            os.environ[key] = value
        elif key in os.environ:
            del os.environ[key]
