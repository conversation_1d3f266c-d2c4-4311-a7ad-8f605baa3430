"""
会话验证简化测试套件
===================

专注于验证会话管理的核心功能，避免复杂依赖
"""

from datetime import UTC, datetime
import os
from unittest.mock import patch
import uuid

import jwt
import pytest


class TestSessionValidationBasics:
    """基础会话验证测试"""

    def test_jwt_secret_environment_consistency(self):
        """测试JWT Secret环境变量一致性"""
        test_secret = "test-unified-secret-key"

        with patch.dict(os.environ, {"SECRET_KEY": test_secret}):
            # 验证backend.auth_fastapi_users.auth中的配置
            auth_secret = os.getenv("SECRET_KEY", "your-very-secret-key")

            # 验证backend.utils.single_device_session中的配置
            session_secret = os.getenv("SECRET_KEY", "your-very-secret-key")

            assert auth_secret == session_secret == test_secret

    def test_jwt_token_generation_and_parsing(self):
        """测试JWT token生成和解析的完整流程"""
        secret = "test-secret-key"
        user_id = str(uuid.uuid4())
        jti = str(uuid.uuid4())

        # 生成token
        now = int(datetime.now(UTC).timestamp())
        payload = {
            "sub": user_id,
            "aud": ["fastapi-users:auth"],
            "iat": now,
            "exp": now + 3600,
            "jti": jti,
        }

        token = jwt.encode(payload, secret, algorithm="HS256")

        # 解析token
        try:
            decoded = jwt.decode(
                token, secret, algorithms=["HS256"], audience=["fastapi-users:auth"]
            )

            assert decoded["sub"] == user_id
            assert decoded["jti"] == jti
            assert decoded["aud"] == ["fastapi-users:auth"]

        except jwt.InvalidTokenError as e:
            pytest.fail(f"JWT解码失败: {e}")

    @pytest.mark.parametrize(
        "missing_field,should_raise",
        [
            ("sub", False),  # sub缺失不会导致解码失败，但字段会为None
            ("jti", False),  # jti缺失不会导致解码失败，但字段会为None
            ("aud", True),  # aud缺失会导致audience验证失败
        ],
    )
    def test_jwt_token_missing_fields(self, missing_field, should_raise):
        """测试JWT token缺失必要字段的处理"""
        secret = "test-secret-key"
        now = int(datetime.now(UTC).timestamp())

        payload = {
            "sub": str(uuid.uuid4()),
            "aud": ["fastapi-users:auth"],
            "iat": now,
            "exp": now + 3600,
            "jti": str(uuid.uuid4()),
        }

        # 删除指定字段
        del payload[missing_field]

        token = jwt.encode(payload, secret, algorithm="HS256")

        if should_raise:
            with pytest.raises(jwt.InvalidTokenError):
                jwt.decode(
                    token, secret, algorithms=["HS256"], audience=["fastapi-users:auth"]
                )
        else:
            # 解码应该成功，但缺失的字段不存在或为None
            decoded = jwt.decode(
                token,
                secret,
                algorithms=["HS256"],
                audience=["fastapi-users:auth"] if missing_field != "aud" else None,
                options={"verify_aud": False} if missing_field == "aud" else {},
            )

            if missing_field == "sub":
                assert "sub" not in decoded
            elif missing_field == "jti":
                assert "jti" not in decoded

    def test_jwt_signature_verification(self):
        """测试JWT签名验证功能"""
        secret1 = "secret-one"
        secret2 = "secret-two"
        user_id = str(uuid.uuid4())

        # 用secret1生成token
        payload = {
            "sub": user_id,
            "aud": ["fastapi-users:auth"],
            "iat": int(datetime.now(UTC).timestamp()),
            "exp": int(datetime.now(UTC).timestamp()) + 3600,
            "jti": str(uuid.uuid4()),
        }

        token = jwt.encode(payload, secret1, algorithm="HS256")

        # 用secret2验证应该失败
        with pytest.raises(jwt.InvalidSignatureError):
            jwt.decode(
                token, secret2, algorithms=["HS256"], audience=["fastapi-users:auth"]
            )

    def test_session_key_format(self):
        """测试会话键格式的一致性"""
        user_id = str(uuid.uuid4())
        expected_key = f"single_device_session:{user_id}"

        # 模拟SingleDeviceSessionManager._get_user_key方法
        def _get_user_key(user_id: str) -> str:
            return f"single_device_session:{user_id}"

        actual_key = _get_user_key(user_id)
        assert actual_key == expected_key
        assert actual_key.startswith("single_device_session:")

    @pytest.mark.parametrize(
        "user_id_type",
        [
            "d0e71520-2417-4b14-a507-346f65c0b174",  # 标准UUID
            "user123",  # 简单字符串
            "test-user-with-dashes",  # 带连字符
        ],
    )
    def test_session_key_with_various_user_ids(self, user_id_type):
        """测试不同格式用户ID的会话键生成"""

        def _get_user_key(user_id: str) -> str:
            return f"single_device_session:{user_id}"

        key = _get_user_key(user_id_type)
        assert key == f"single_device_session:{user_id_type}"
        assert user_id_type in key


class TestSessionErrorHandling:
    """会话错误处理测试"""

    def test_invalid_jwt_format_handling(self):
        """测试无效JWT格式的处理"""
        invalid_tokens = [
            "not.a.jwt",
            "invalid-format",
            "",
            "too.many.segments.here.invalid",
        ]

        secret = "test-secret"

        for invalid_token in invalid_tokens:
            with pytest.raises(jwt.DecodeError):
                jwt.decode(
                    invalid_token,
                    secret,
                    algorithms=["HS256"],
                    audience=["fastapi-users:auth"],
                )

    def test_expired_token_handling(self):
        """测试过期token的处理"""
        secret = "test-secret"
        user_id = str(uuid.uuid4())

        # 创建已过期的token
        past_time = int(datetime.now(UTC).timestamp()) - 3600
        payload = {
            "sub": user_id,
            "aud": ["fastapi-users:auth"],
            "iat": past_time,
            "exp": past_time + 1,  # 1秒后过期（已经过期）
            "jti": str(uuid.uuid4()),
        }

        expired_token = jwt.encode(payload, secret, algorithm="HS256")

        # 默认情况下应该抛出过期异常
        with pytest.raises(jwt.ExpiredSignatureError):
            jwt.decode(
                expired_token,
                secret,
                algorithms=["HS256"],
                audience=["fastapi-users:auth"],
            )

        # 跳过过期验证应该成功
        decoded = jwt.decode(
            expired_token,
            secret,
            algorithms=["HS256"],
            audience=["fastapi-users:auth"],
            options={"verify_exp": False},
        )

        assert decoded["sub"] == user_id

    def test_wrong_audience_handling(self):
        """测试错误audience的处理"""
        secret = "test-secret"
        user_id = str(uuid.uuid4())

        # 创建错误audience的token
        payload = {
            "sub": user_id,
            "aud": ["wrong-audience"],
            "iat": int(datetime.now(UTC).timestamp()),
            "exp": int(datetime.now(UTC).timestamp()) + 3600,
            "jti": str(uuid.uuid4()),
        }

        token = jwt.encode(payload, secret, algorithm="HS256")

        # 使用正确的audience验证应该失败
        with pytest.raises(jwt.InvalidAudienceError):
            jwt.decode(
                token, secret, algorithms=["HS256"], audience=["fastapi-users:auth"]
            )


class TestSessionEnvironmentConfig:
    """会话环境配置测试"""

    def test_secret_key_fallback_behavior(self):
        """测试SECRET_KEY回退行为"""
        # 测试未设置环境变量时的默认值
        with patch.dict(os.environ, {}, clear=True):
            if "SECRET_KEY" in os.environ:
                del os.environ["SECRET_KEY"]

            default_secret = os.getenv("SECRET_KEY", "your-very-secret-key")
            assert default_secret == "your-very-secret-key"

    def test_environment_variable_priority(self):
        """测试环境变量优先级"""
        test_secret = "priority-test-secret"

        with patch.dict(os.environ, {"SECRET_KEY": test_secret}):
            # SECRET_KEY 应该优先于默认值
            actual_secret = os.getenv("SECRET_KEY", "default-value")
            assert actual_secret == test_secret
            assert actual_secret != "default-value"

    @pytest.mark.parametrize(
        "env_value",
        [
            "production-secret-key",
            "dev-secret-123",
            "test-env-secret",
        ],
    )
    def test_various_secret_values(self, env_value):
        """测试各种SECRET_KEY值的处理"""
        with patch.dict(os.environ, {"SECRET_KEY": env_value}):
            retrieved_secret = os.getenv("SECRET_KEY")
            assert retrieved_secret == env_value
