#!/bin/bash
# 🔒 Docker容器权限设置脚本
# 统一管理所有容器的文件权限，减少Dockerfile重复代码
# 基于2025年Docker安全最佳实践

set -e  # 遇到错误立即退出

echo "🔒 开始设置容器文件权限..."

# 获取当前用户信息
CURRENT_USER=$(whoami)
echo "📋 当前用户: $CURRENT_USER"

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p screenshots payment_screenshots downloads .prefs test_data results temp logs

# 设置所有者
echo "👤 设置文件所有者..."
chown -R ${CURRENT_USER}:${CURRENT_USER} /app

# 🔒 2025年安全最佳实践：严格的文件权限
echo "🔒 设置基础文件权限..."
find /app -type f -exec chmod 644 {} \;
find /app -type d -exec chmod 755 {} \;

# 🔒 应用代码只读
echo "🔒 设置应用代码为只读..."
if [ -d "/app/backend" ]; then
    chmod -R 644 /app/backend
    find /app/backend -type d -exec chmod 755 {} \;
fi

if [ -d "/app/app" ]; then
    chmod -R 644 /app/app
    find /app/app -type d -exec chmod 755 {} \;
fi

if [ -d "/app/celery_worker" ]; then
    chmod -R 644 /app/celery_worker
    find /app/celery_worker -type d -exec chmod 755 {} \;
fi

if [ -d "/app/config" ]; then
    chmod -R 644 /app/config
    find /app/config -type d -exec chmod 755 {} \;
fi

# 🔒 数据目录可写权限（将通过tmpfs或volume挂载）
# 使用750权限提高安全性（只有用户和组可访问）
echo "🔒 设置数据目录权限..."
for dir in screenshots payment_screenshots downloads .prefs test_data results temp logs; do
    if [ -d "/app/$dir" ]; then
        chmod -R 750 /app/$dir
        echo "✅ 设置 /app/$dir 权限为 750"
    fi
done

echo "✅ 权限设置完成！"
echo "📋 权限总结："
echo "   - 应用代码: 644/755 (只读)"
echo "   - 数据目录: 750 (用户和组可写)"
echo "   - 所有者: ${CURRENT_USER}:${CURRENT_USER}"
