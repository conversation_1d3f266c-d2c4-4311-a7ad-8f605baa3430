# 统一邮件轮询服务 Dockerfile
# ================================
#
# 优化后的邮件轮询服务容器
# 集成邮件轮询、解析、处理和签证下载功能
#
# 特性：
# - 统一架构：整合所有邮件相关功能
# - 高性能：优化的解析器和处理器
# - 完整功能：包含签证PDF自动下载
# - 易维护：模块化设计，统一配置管理

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 🔧 2025年安全最佳实践：先创建非root用户
# 使用固定的UID/GID，避免权限冲突
RUN groupadd --system --gid 1003 emailsvc && \
    useradd --system --uid 1003 --gid emailsvc --home /app --shell /bin/bash emailsvc

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV TZ=UTC

# 安装系统依赖并设置时区（合并RUN层优化）
RUN apt-get update && apt-get install -y \
    # 时区支持
    tzdata \
    # 网络工具（用于健康检查）
    curl \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    # 设置时区为UTC（数据库层统一时区重构）
    && ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime \
    && echo "$TZ" > /etc/timezone

# 复制依赖文件，设置正确的所有者
COPY --chown=emailsvc:emailsvc pyproject.toml .

# 🔧 最佳实践：为Playwright指定系统级安装路径
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# 安装Python依赖、Playwright并创建目录（合并RUN层优化）
RUN pip install --no-cache-dir -e . && \
    playwright install --with-deps chromium && \
    mkdir -p logs temp

# 复制项目代码，设置正确的所有者
# 复制邮件轮询服务需要的所有代码和配置
COPY --chown=emailsvc:emailsvc . .

# 设置正确的目录权限
RUN chown -R emailsvc:emailsvc /app

# 健康检查
# 通过HTTP端点检查邮件轮询服务状态
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 🔧 2025年安全最佳实践：切换到非root用户
# 注意：浏览器已经安装在系统级目录，emailsvc用户只需要读取权限
USER emailsvc

# 暴露健康检查端口
EXPOSE 8001

# 启动邮件轮询服务
CMD ["python", "email_service/main.py"]

# 🔥 生产环境优化说明：
#
# 1. 资源限制：
#    - 内存：建议限制在 512MB-1GB
#    - CPU：0.5-1.0 核心即可
#
# 2. 扩展策略：
#    - 邮件轮询服务通常只需要1个实例
#    - 如果邮箱数量很多，可以考虑按邮箱分组部署多个实例
#
# 3. 监控建议：
#    - 监控邮件轮询频率和成功率
#    - 监控IMAP连接状态
#    - 监控HTTP通知到FastAPI的成功率
#
# 4. 容错机制：
#    - 邮件轮询失败时自动重试
#    - FastAPI通知失败时的降级处理
#    - 网络中断时的自动恢复
