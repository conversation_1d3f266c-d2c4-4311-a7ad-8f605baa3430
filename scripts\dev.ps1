# 🚀 开发环境启动脚本
# 参考FastAPI官方模板最佳实践

Write-Host "🚀 启动Visa Automator开发环境..." -ForegroundColor Green

# 检查Docker是否运行（跨平台）
Write-Host "🔍 检查Docker服务状态..." -ForegroundColor Yellow
try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker服务运行正常" -ForegroundColor Green
    }
    else {
        throw "Docker daemon not responding"
    }
}
catch {
    Write-Host "❌ Docker未运行或未安装，请先启动Docker服务" -ForegroundColor Red
    Write-Host "💡 Windows: 启动Docker Desktop" -ForegroundColor Yellow
    Write-Host "💡 Linux: sudo systemctl start docker" -ForegroundColor Yellow
    Write-Host "💡 macOS: 启动Docker Desktop" -ForegroundColor Yellow
    exit 1
}

# 启动后端服务（使用override配置）
Write-Host "📦 启动后端服务..." -ForegroundColor Yellow
docker compose up -d postgres redis visa-automator celery-worker email-polling

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
docker compose ps

# 启动前端开发服务器
Write-Host "🎨 启动前端开发服务器..." -ForegroundColor Yellow
Set-Location frontend
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"
Set-Location ..

Write-Host "✅ 开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  前端开发服务器: http://localhost:5173" -ForegroundColor White
Write-Host "  后端API:        http://localhost:8001" -ForegroundColor White
Write-Host "  API文档:        http://localhost:8001/docs" -ForegroundColor White
Write-Host "  数据库:         localhost:5432" -ForegroundColor White
Write-Host "  Redis:          localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "📝 停止服务: docker compose down" -ForegroundColor Yellow
