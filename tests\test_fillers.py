"""
表单填写器测试
=============

测试VietnamFiller的表单填写逻辑、验证和错误处理
"""

from unittest.mock import Mock, patch

from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
import pytest

from app.data.model import VietnamEVisaApplicant
from app.fillers.vietnam_filler import VietnamFiller


class TestVietnamFillerInit:
    """VietnamFiller初始化测试"""

    def test_filler_initialization(self):
        """测试Filler基本初始化"""
        filler = VietnamFiller()

        assert filler.locators == {}
        assert filler.settings == {}
        assert filler.base_url == ""
        assert filler._vietnam_addresses is None
        assert filler.session_id is None
        assert filler.application_number is None

    def test_prepare_with_real_config(self):
        """测试使用真实配置准备Filler"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        # 使用真实的配置加载
        engine = VisaAutomationEngine()
        filler = VietnamFiller()

        # 使用真实配置准备
        filler.prepare(engine.locators, engine.settings)

        # 验证配置正确加载
        assert filler.locators is not None
        assert filler.settings is not None
        assert filler.base_url != ""
        assert "vietnam_evisa_url" in filler.settings

    def test_prepare_missing_locators(self):
        """测试缺少定位器配置"""
        filler = VietnamFiller()

        all_locators = {}  # 缺少vietnam_evisa配置
        settings = {"vietnam_evisa_url": "https://evisa.gov.vn/"}

        with pytest.raises(
            ValueError, match="Configuration for 'vietnam_evisa' locators is missing"
        ):
            filler.prepare(all_locators, settings)

    def test_prepare_missing_url(self):
        """测试缺少URL配置"""
        filler = VietnamFiller()

        all_locators = {"vietnam_evisa": {"test": "selector"}}
        settings = {}  # 缺少URL配置

        with pytest.raises(ValueError, match="Vietnam E-Visa URL not configured"):
            filler.prepare(all_locators, settings)


class TestVietnamFillerUtilityMethods:
    """工具方法测试"""

    @pytest.fixture
    def prepared_filler(self):
        """准备好的Filler实例 - 使用真实配置"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        # 使用真实的配置加载
        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    def test_get_locator_with_real_config(self, prepared_filler):
        """测试使用真实配置获取定位器"""
        # 测试获取真实的定位器
        locator = prepared_filler.get_locator("homepage.apply_now_button")

        # 验证返回的是字符串（真实的选择器）
        if locator is not None:
            assert isinstance(locator, str)
            assert len(locator) > 0

    def test_get_locator_missing_key(self, prepared_filler):
        """测试不存在的定位器路径"""
        result = prepared_filler.get_locator("nonexistent.path")
        assert result is None

    def test_safe_action_success(self, prepared_filler):
        """测试安全操作成功"""
        mock_action = Mock()

        result = prepared_filler._safe_action(mock_action, "test action")
        assert result is True
        mock_action.assert_called_once()

    def test_safe_action_timeout_error(self, prepared_filler):
        """测试安全操作超时错误"""
        mock_action = Mock(side_effect=PlaywrightTimeoutError("Timeout occurred"))

        result = prepared_filler._safe_action(mock_action, "test action")
        assert result is False

    def test_safe_action_general_exception(self, prepared_filler):
        """测试安全操作一般异常"""
        mock_action = Mock(side_effect=Exception("General error"))

        result = prepared_filler._safe_action(mock_action, "test action")
        assert result is False

    def test_calculate_end_date_30_days(self, prepared_filler):
        """测试30天签证结束日期计算"""
        start_date = "01/01/2025"
        result = prepared_filler._calculate_end_date(start_date, "30天")
        expected = "30/01/2025"  # 29天后
        assert result == expected

    def test_calculate_end_date_90_days(self, prepared_filler):
        """测试90天签证结束日期计算"""
        start_date = "01/01/2025"
        result = prepared_filler._calculate_end_date(start_date, "90天")
        expected = "31/03/2025"  # 89天后
        assert result == expected

    def test_calculate_end_date_unknown_duration(self, prepared_filler):
        """测试未知签证时长"""
        start_date = "01/01/2025"
        result = prepared_filler._calculate_end_date(start_date, "unknown")
        assert result is None

    def test_calculate_end_date_invalid_format(self, prepared_filler):
        """测试无效日期格式"""
        start_date = "invalid-date"
        result = prepared_filler._calculate_end_date(start_date, "30天")
        assert result is None


class TestVietnamFillerFormFilling:
    """表单填写测试 - 只Mock页面交互，使用真实业务逻辑"""

    @pytest.fixture
    def prepared_filler(self):
        """准备好的Filler实例"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    @pytest.fixture
    def sample_applicant(self):
        """示例申请人"""
        return VietnamEVisaApplicant(
            passport_number="*********",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="CHINA",
            sex="MALE",
            email="<EMAIL>",
            telephone_number="13800138000",
            permanent_address="Beijing, China",
            contact_address="Beijing, China",
            visa_entry_type="single",
            visa_validity_duration="30天",
            visa_start_date="01/07/2025",
            intended_entry_gate="NHAT_TAN_BRIDGE",
            purpose_of_entry="Tourist",
            visited_vietnam_last_year=False,
            has_vietnam_contact=False,
        )

    @pytest.fixture
    def mock_page(self):
        """Mock页面对象 - 这是外部依赖"""
        page = Mock()
        page.locator.return_value = Mock()
        page.wait_for_timeout = Mock()
        page.wait_for_load_state = Mock()
        page.screenshot = Mock()
        return page

    def test_form_filling_workflow(self, prepared_filler, sample_applicant, mock_page):
        """测试表单填写工作流程"""
        # 这里测试真实的业务逻辑，只Mock页面交互

        # Mock页面元素
        mock_element = Mock()
        mock_page.locator.return_value = mock_element

        # 测试填写流程（这里使用真实的业务逻辑）
        try:
            result = prepared_filler.fill_step1_personal_info(
                mock_page, sample_applicant
            )
            # 由于页面交互被Mock，可能会失败，但业务逻辑应该被执行
            assert isinstance(result, bool)
        except Exception as e:
            # 预期可能失败，因为页面交互被Mock
            assert "locator" in str(e).lower() or "element" in str(e).lower()

    def test_input_and_enter_method(self, prepared_filler, mock_page):
        """测试输入和回车方法"""
        # 测试真实的输入方法逻辑
        mock_element = Mock()
        mock_page.locator.return_value = mock_element

        result = prepared_filler._input_and_enter(
            mock_page, "#test-input", "test value", "test description"
        )

        # 验证方法被调用
        assert isinstance(result, bool)


class TestVietnamFillerCaptcha:
    """验证码处理测试"""

    @pytest.fixture
    def prepared_filler(self):
        """准备好的Filler实例"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    @pytest.fixture
    def mock_page(self):
        """Mock页面对象"""
        page = Mock()
        page.locator.return_value = Mock()
        page.wait_for_timeout = Mock()
        page.screenshot = Mock()
        return page

    def test_handle_captcha_page_success(self, prepared_filler, mock_page):
        """测试验证码处理成功"""
        # Mock页面元素
        mock_element = Mock()
        mock_page.locator.return_value = mock_element

        # 只Mock外部验证码解决服务
        with patch("app.fillers.vietnam_filler.solve_captcha") as mock_solve:
            mock_solve.return_value = "12345"

            # 测试验证码处理
            result = prepared_filler.handle_captcha_page(mock_page)

            # 验证结果
            assert isinstance(result, bool)

            # 验证验证码解决服务被调用
            if result:
                mock_solve.assert_called()

    def test_handle_captcha_page_solve_failure(self, prepared_filler, mock_page):
        """测试验证码解决失败"""
        # Mock页面元素
        mock_element = Mock()
        mock_page.locator.return_value = mock_element

        # Mock验证码解决失败
        with patch("app.fillers.vietnam_filler.solve_captcha") as mock_solve:
            mock_solve.return_value = None

            # 测试验证码处理
            result = prepared_filler.handle_captcha_page(mock_page)

            # 验证结果
            assert isinstance(result, bool)


class TestVietnamFillerAddressHandling:
    """地址处理测试 - 测试真实的业务逻辑"""

    @pytest.fixture
    def prepared_filler(self):
        """准备好的Filler实例"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    @pytest.fixture
    def sample_applicant(self):
        """示例申请人"""
        return VietnamEVisaApplicant(
            passport_number="*********",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="CHINA",
            sex="MALE",
        )

    def test_get_vietnam_addresses_with_real_file(self, prepared_filler):
        """测试使用真实文件获取越南地址"""
        # 测试真实的文件读取逻辑
        addresses = prepared_filler._get_vietnam_addresses()

        # 验证结果
        if addresses is not None:
            assert isinstance(addresses, dict)
            # 验证地址结构
            for city, address_list in addresses.items():
                assert isinstance(city, str)
                assert isinstance(address_list, list)

    def test_ensure_addresses_with_real_logic(self, prepared_filler, sample_applicant):
        """测试地址确保逻辑"""
        # 测试真实的地址处理逻辑
        prepared_filler._ensure_addresses(sample_applicant)

        # 验证地址数据被设置 - _ensure_addresses实际上设置的是这些字段
        assert sample_applicant.permanent_address is not None
        assert sample_applicant.contact_address is not None
        assert sample_applicant.emergency_address is not None


class TestVietnamFillerExtraction:
    """数据提取测试"""

    @pytest.fixture
    def prepared_filler(self):
        """准备好的Filler实例"""
        from app.core.visa_automation_engine import VisaAutomationEngine

        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    def test_extract_application_number_success(self, prepared_filler):
        """测试成功提取申请编号"""
        # Mock页面内容
        mock_page = Mock()
        mock_page.content.return_value = "Application No: VN12345678"

        result = prepared_filler.extract_application_number(mock_page)

        # 验证结果
        if result:
            assert isinstance(result, str)
            assert "VN" in result

    def test_extract_application_number_not_found(self, prepared_filler):
        """测试未找到申请编号"""
        # Mock页面内容
        mock_page = Mock()
        mock_page.content.return_value = "No application number here"

        result = prepared_filler.extract_application_number(mock_page)

        # 验证结果
        assert result is None

    def test_extract_payment_info_success(self, prepared_filler):
        """测试成功提取支付信息"""
        # Mock页面内容
        mock_page = Mock()
        mock_page.content.return_value = "*********901234567890 25 USD"
        mock_page.wait_for_timeout = Mock()

        # Mock extract_application_number方法
        prepared_filler.extract_application_number = Mock(
            return_value="*********901234567890"
        )

        result = prepared_filler.extract_payment_info(mock_page)

        # 验证结果 - extract_payment_info总是返回一个字典
        assert isinstance(result, dict)
        assert "application_number" in result
        assert "payment_amount" in result

    def test_extract_payment_info_not_found(self, prepared_filler):
        """测试未找到支付信息"""
        # Mock页面内容
        mock_page = Mock()
        mock_page.content.return_value = "No payment info here"
        mock_page.wait_for_timeout = Mock()

        # Mock extract_application_number方法返回None
        prepared_filler.extract_application_number = Mock(return_value=None)

        result = prepared_filler.extract_payment_info(mock_page)

        # 验证结果 - extract_payment_info总是返回一个字典，即使找不到信息
        assert isinstance(result, dict)
        assert "application_number" in result
        assert "payment_amount" in result
        assert result["application_number"] is None
        assert result["payment_amount"] is None
