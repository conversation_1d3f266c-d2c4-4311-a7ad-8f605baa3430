"""fix_migration_history_timezone_default

Revision ID: c7202ff5bc32
Revises: 8c9231189790
Create Date: 2025-07-04 10:12:42.123456

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c7202ff5bc32'
down_revision: Union[str, None] = '8c9231189790'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    修复migration_history.applied_at字段的时区默认值从Asia/Shanghai改为UTC。
    这是对之前迁移8c9231189790的补丁，修复遗漏的migration_history表字段。

    同时向migration_history表插入此次迁移的记录。
    """
    print("🔧 修复migration_history.applied_at字段的时区默认值...")

    # 修复 migration_history 表的 applied_at 字段默认值
    op.alter_column('migration_history', 'applied_at',
                   server_default=sa.text('now()'))

    print("✅ migration_history.applied_at字段默认值已修复为UTC")

        # 向migration_history表插入此次迁移的记录
    print("📝 记录此次迁移到migration_history表...")

    # 先记录之前的迁移8c9231189790（如果还没有记录的话）
    op.execute("""
        INSERT INTO migration_history (
            version_num,
            migration_name,
            description,
            tables_affected,
            applied_at,
            success
        )
        SELECT
            '8c9231189790',
            'fix_timezone_defaults_to_utc_v2',
            '更新所有时间字段的DEFAULT约束从Asia/Shanghai时区改为UTC - 修复23个时间字段',
            'applicant,application,automation_logs,file,order,user,user_payment,visa_payment,visa_status_history',
            now(),
            true
        WHERE NOT EXISTS (
            SELECT 1 FROM migration_history WHERE version_num = '8c9231189790'
        )
    """)

    # 记录此次迁移
    op.execute("""
        INSERT INTO migration_history (
            version_num,
            migration_name,
            description,
            tables_affected,
            applied_at,
            success
        ) VALUES (
            'c7202ff5bc32',
            'fix_migration_history_timezone_default',
            '修复migration_history.applied_at字段的时区默认值从Asia/Shanghai改为UTC - 补丁迁移',
            'migration_history',
            now(),
            true
        )
    """)

    print("✅ 迁移记录已成功插入migration_history表（包括之前遗漏的记录）")


def downgrade() -> None:
    """
    回滚操作：将migration_history.applied_at字段的DEFAULT约束改回Asia/Shanghai时区。
    同时删除此次迁移的记录。
    """
    print("🔄 回滚migration_history.applied_at字段的时区默认值...")

    # 回滚 migration_history 表的 applied_at 字段默认值
    op.alter_column('migration_history', 'applied_at',
                   server_default=sa.text("timezone('Asia/Shanghai', now())"))

    print("✅ migration_history.applied_at字段默认值已回滚为Asia/Shanghai")

        # 删除此次迁移的记录
    print("🗑️ 删除此次迁移的记录...")

    op.execute("""
        DELETE FROM migration_history
        WHERE version_num = 'c7202ff5bc32'
    """)

    # 同时删除之前迁移的记录（如果是由这次迁移插入的）
    op.execute("""
        DELETE FROM migration_history
        WHERE version_num = '8c9231189790'
    """)

    print("✅ 迁移记录已从migration_history表删除（包括相关的迁移记录）")
