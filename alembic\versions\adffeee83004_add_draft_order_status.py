"""add_draft_order_status

Revision ID: adffeee83004
Revises: 44d7547e971d
Create Date: 2025-06-30 14:44:33.273272

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'adffeee83004'
down_revision: Union[str, None] = '44d7547e971d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """添加草稿状态支持到Order表"""

    print("\n🎯 开始添加草稿状态支持...")
    print("=" * 50)

    # 第一步：删除现有的order_status约束
    print("🗑️ 删除现有的order_status约束...")
    try:
        op.drop_constraint("simple_order_status_check", "order", type_="check")
        print("✅ 成功删除 simple_order_status_check 约束")
    except Exception as e:
        print(f"⚠️ 删除约束失败（可能不存在）: {e}")

    # 第二步：添加新的约束，包含draft状态
    print("✅ 添加新的order_status约束（包含draft状态）...")
    try:
        op.execute("""
            ALTER TABLE "order" ADD CONSTRAINT order_status_with_draft_check
            CHECK (order_status IN ('draft', 'created', 'cancelled'))
        """)
        print("✅ 成功添加新约束：draft, created, cancelled")
    except Exception as e:
        print(f"❌ 添加新约束失败: {e}")
        raise

    # 第三步：更新模型注释
    print("📝 更新字段注释...")
    try:
        op.execute("""
            COMMENT ON COLUMN "order".order_status IS '订单状态: draft(草稿), created(已创建), cancelled(已取消)'
        """)
        print("✅ 字段注释更新完成")
    except Exception as e:
        print(f"⚠️ 注释更新失败: {e}")

    print("\n🎉 草稿状态支持添加完成！")


def downgrade() -> None:
    """回滚草稿状态支持"""

    print("\n⚠️ 开始回滚草稿状态支持...")
    print("=" * 50)

    # 删除新约束
    try:
        op.drop_constraint("order_status_with_draft_check", "order", type_="check")
        print("✅ 删除新约束")
    except Exception as e:
        print(f"⚠️ 删除新约束失败: {e}")

    # 恢复原约束
    try:
        op.execute("""
            ALTER TABLE "order" ADD CONSTRAINT simple_order_status_check
            CHECK (order_status IN ('created', 'cancelled'))
        """)
        print("✅ 恢复原约束")
    except Exception as e:
        print(f"❌ 恢复原约束失败: {e}")

    # 清理draft状态数据（如果有）
    print("🧹 清理draft状态数据...")
    try:
        result = op.get_bind().execute(
            sa.text("UPDATE \"order\" SET order_status = 'created' WHERE order_status = 'draft'")
        )
        print(f"✅ 已将 {result.rowcount} 条draft记录转换为created状态")
    except Exception as e:
        print(f"⚠️ 数据清理失败: {e}")

    print("✅ 回滚完成")
