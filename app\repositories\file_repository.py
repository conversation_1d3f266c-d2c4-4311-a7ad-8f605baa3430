"""
文件Repository - 专门处理文件数据访问
"""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import desc

from app.data.models.file import File
from app.repositories.base import SQLAlchemyRepository


class FileRepository(SQLAlchemyRepository[File, UUID]):
    """文件Repository - 专门处理文件数据访问"""

    def __init__(self, session: AsyncSession):
        """
        接受Session注入
        """
        super().__init__(session, File)

    async def get_by_application_id(self, application_id: UUID) -> list[File]:
        """根据申请ID获取文件列表"""
        result = await self.session.execute(
            select(File)
            .where(File.application_id == application_id)
            .order_by(desc(File.created_at))
        )
        return list(result.scalars().all())

    async def get_by_file_type(
        self, application_id: UUID, file_type: str
    ) -> File | None:
        """根据申请ID和文件类型获取文件"""
        result = await self.session.execute(
            select(File).where(
                File.application_id == application_id, File.file_type == file_type
            )
        )
        return result.scalar_one_or_none()

    async def delete_by_application_id(self, application_id: UUID) -> int:
        """删除申请相关的所有文件"""
        from sqlalchemy import delete

        delete_stmt = delete(File).where(File.application_id == application_id)
        result = await self.session.execute(delete_stmt)
        return result.rowcount or 0
