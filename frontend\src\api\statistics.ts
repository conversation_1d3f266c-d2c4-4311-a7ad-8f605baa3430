import { api } from './request'
import type { ApiResponse } from './types'

// 统计数据类型定义
export interface BasicStats {
  total_applications: number
  successful_applications: number
  failed_applications: number
  pending_applications: number
  success_rate: number
}

export interface TimeSeriesData {
  date: string
  applications: number
  successful: number
}

export interface StatusDistribution {
  status: string
  count: number
  percentage: number
}

export interface PaymentStats {
  paid_count: number
  pending_count: number
  failed_count: number
  total_amount: number
}

export interface VisaTypeStats {
  visa_type: string
  count: number
  percentage: number
}

export interface ProcessingTimeStats {
  average_hours: number
  median_hours: number
  min_hours: number
  max_hours: number
  total_processed: number
}

export interface StatisticsData {
  basic_stats: BasicStats
  time_series: TimeSeriesData[]
  status_distribution: StatusDistribution[]
  payment_stats: PaymentStats
  visa_type_stats: VisaTypeStats[]
  processing_time_stats: ProcessingTimeStats
  period: string
  last_updated: string
}

// 统计API
export const statisticsApi = {
  /**
   * 获取统计数据
   */
  async getStatistics(period: string = '7d'): Promise<ApiResponse<StatisticsData>> {
    return api.get<ApiResponse<StatisticsData>>(`/api/visa/admin/statistics?period=${period}`)
  },

  /**
   * 获取基础统计
   */
  async getBasicStats(): Promise<ApiResponse<BasicStats>> {
    return api.get<ApiResponse<BasicStats>>('/api/visa/admin/basic-stats')
  },

  /**
   * 获取实时统计（用于Dashboard）- 简化HTTP统计数据
   */
  async getRealtimeStats(): Promise<
    ApiResponse<BasicStats & { today_applications: number; realtime_processing: number }>
  > {
    return api.get('/api/visa/admin/realtime-stats')
  },

  /**
   * 获取订单统计
   */
  async getOrderStats(): Promise<ApiResponse<Record<string, unknown>>> {
    return api.get('/api/orders/stats')
  },
}
