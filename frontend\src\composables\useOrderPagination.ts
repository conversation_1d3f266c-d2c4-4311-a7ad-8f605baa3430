/**
 * 订单分页和虚拟列表组合式函数
 * 解决HH Notepad检查点8：性能优化缺失问题
 */

import type { Ref } from 'vue'
import { computed, readonly, ref, watch } from 'vue'

// 分页配置
interface PaginationOptions {
  pageSize?: number
  defaultPage?: number
  maxItems?: number // 最大内存中保留的项目数
}

// 虚拟列表配置
interface VirtualListOptions {
  itemHeight?: number
  visibleCount?: number
  bufferSize?: number
}

/**
 * 订单分页管理
 */
export function useOrderPagination<
  T extends { orderNo: string; timestamp: string; status?: string; userId?: string },
>(items: Ref<T[]>, options: PaginationOptions = {}) {
  const {
    pageSize = 20,
    defaultPage = 1,
    maxItems = 1000, // 防止内存泄漏
  } = options

  const currentPage = ref(defaultPage)
  const searchText = ref('')
  const statusFilter = ref<string>('')
  const userFilter = ref<string>('')

  // 过滤后的项目
  const filteredItems = computed(() => {
    let result = items.value

    // 文本搜索过滤
    if (searchText.value.trim()) {
      const searchLower = searchText.value.toLowerCase()
      result = result.filter(
        (item) =>
          item.orderNo.toLowerCase().includes(searchLower) ||
          JSON.stringify(item).toLowerCase().includes(searchLower),
      )
    }

    // 状态过滤
    if (statusFilter.value) {
      result = result.filter((item) => 'status' in item && item.status === statusFilter.value)
    }

    // 用户过滤
    if (userFilter.value) {
      result = result.filter((item) => 'userId' in item && item.userId === userFilter.value)
    }

    // 按时间排序（最新在前）
    return result.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  })

  // 总页数
  const totalPages = computed(() => Math.ceil(filteredItems.value.length / pageSize))

  // 当前页数据
  const currentPageItems = computed(() => {
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    return filteredItems.value.slice(start, end)
  })

  // 分页信息
  const paginationInfo = computed(() => ({
    currentPage: currentPage.value,
    totalPages: totalPages.value,
    totalItems: filteredItems.value.length,
    pageSize,
    hasNext: currentPage.value < totalPages.value,
    hasPrev: currentPage.value > 1,
    startIndex: (currentPage.value - 1) * pageSize + 1,
    endIndex: Math.min(currentPage.value * pageSize, filteredItems.value.length),
  }))

  // 内存管理：防止数据过多导致性能问题
  watch(
    items,
    (newItems) => {
      if (newItems.length > maxItems) {
        console.warn(
          `[useOrderPagination] 数据量过大(${newItems.length}/${maxItems})，建议优化数据管理`,
        )

        // 可以实施数据清理策略
        // 例如：只保留最新的maxItems条数据
        // 🔧 修复：移除调试日志

        // 这里可以通过events或store通知清理
        // 暂时只是警告
      }
    },
    { deep: true },
  )

  // 分页控制方法
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++
    }
  }

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }

  const resetPage = () => {
    currentPage.value = 1
  }

  // 过滤器控制
  const setSearch = (text: string) => {
    searchText.value = text
    resetPage() // 搜索时重置到第一页
  }

  const setStatusFilter = (status: string) => {
    statusFilter.value = status
    resetPage()
  }

  const setUserFilter = (userId: string) => {
    userFilter.value = userId
    resetPage()
  }

  const clearFilters = () => {
    searchText.value = ''
    statusFilter.value = ''
    userFilter.value = ''
    resetPage()
  }

  return {
    // 数据
    currentPageItems,
    filteredItems,
    paginationInfo,

    // 过滤器状态
    searchText: readonly(searchText),
    statusFilter: readonly(statusFilter),
    userFilter: readonly(userFilter),

    // 分页控制
    goToPage,
    nextPage,
    prevPage,
    resetPage,

    // 过滤器控制
    setSearch,
    setStatusFilter,
    setUserFilter,
    clearFilters,
  }
}

/**
 * 虚拟列表支持
 * 用于处理大量数据的渲染性能优化
 */
export function useVirtualList<T>(items: Ref<T[]>, options: VirtualListOptions = {}) {
  const { itemHeight = 60, visibleCount = 15, bufferSize = 5 } = options

  const scrollTop = ref(0)
  const containerHeight = ref(visibleCount * itemHeight)

  // 可视区域计算
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(start + visibleCount + bufferSize, items.value.length)

    return {
      start: Math.max(0, start - bufferSize),
      end,
      visibleStart: start,
      visibleEnd: Math.min(start + visibleCount, items.value.length),
    }
  })

  // 可见的项目
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      offset: (start + index) * itemHeight,
    }))
  })

  // 总高度
  const totalHeight = computed(() => items.value.length * itemHeight)

  // 滚动处理
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  // 滚动到指定项
  const scrollToItem = (index: number) => {
    scrollTop.value = index * itemHeight
  }

  // 滚动到顶部
  const scrollToTop = () => {
    scrollTop.value = 0
  }

  // 滚动到底部
  const scrollToBottom = () => {
    scrollTop.value = Math.max(0, totalHeight.value - containerHeight.value)
  }

  return {
    // 虚拟列表数据
    visibleItems,
    totalHeight,
    visibleRange,

    // 滚动状态
    scrollTop: readonly(scrollTop),
    containerHeight: readonly(containerHeight),

    // 滚动控制
    handleScroll,
    scrollToItem,
    scrollToTop,
    scrollToBottom,

    // 配置
    itemHeight,
  }
}

/**
 * 组合分页和虚拟列表的高性能订单列表
 */
export function usePerformantOrderList<
  T extends { orderNo: string; timestamp: string; status?: string; userId?: string },
>(
  items: Ref<T[]>,
  paginationOptions: PaginationOptions = {},
  virtualOptions: VirtualListOptions = {},
) {
  // 分页管理
  const pagination = useOrderPagination(items, paginationOptions)

  // 虚拟列表（应用在当前页数据上）
  const virtualList = useVirtualList(pagination.currentPageItems, virtualOptions)

  // 性能监控
  const performanceStats = computed(() => ({
    totalItems: items.value.length,
    filteredItems: pagination.filteredItems.value.length,
    currentPageItems: pagination.currentPageItems.value.length,
    visibleItems: virtualList.visibleItems.value.length,
    memoryEfficiency: `${((virtualList.visibleItems.value.length / items.value.length) * 100).toFixed(2)}%`,
  }))

  return {
    // 分页功能
    ...pagination,

    // 虚拟列表功能
    ...virtualList,

    // 性能信息
    performanceStats,
  }
}
