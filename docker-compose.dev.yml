# 🔧 开发环境完整配置 - 包含前端容器
# 使用方式：docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
#
# 🎯 开发环境特点：
# - 包含前端开发容器（支持热重载）
# - 所有服务暴露端口供直接访问和调试
# - 启用代码热重载
# - 适合前后端同时开发

services:
  # PostgreSQL - 开发环境配置
  postgres:
    restart: "no"  # 开发环境不自动重启
    ports:
      - "5432:5432"  # 暴露端口供本地连接

  # Redis - 开发环境配置
  redis:
    restart: "no"
    ports:
      - "6379:6379"  # 暴露端口供本地连接

  # FastAPI后端 - 开发环境配置
  visa-automator:
    restart: "no"
    ports:
      - "8000:8000"  # 开发环境也使用8000端口，与生产环境统一
    volumes:
      - ./:/app  # 开发环境：挂载源码实现热重载
      - /app/.pki  # 排除.pki目录避免权限问题
    command:
      [
        "uvicorn",
        "backend.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",  # 开发环境：启用热重载
        "--reload-exclude",
        ".pki",  # 排除.pki目录避免权限错误
        "--reload-exclude",
        ".git",  # 排除git目录
        "--reload-exclude",
        ".pytest_cache",  # 排除pytest缓存
        "--reload-exclude",
        "__pycache__",  # 排除Python缓存
      ]
    environment:
      - ENVIRONMENT=local  # 开发环境标识（使用local以启用debug模式）
      - DEBUG=true

  # 邮件轮询服务 - 开发环境配置
  email-polling:
    restart: "no"
    ports:
      - "8002:8001"  # 暴露健康检查端口
    volumes:
      - ./:/app  # 开发环境：挂载源码

  # Celery Worker - 开发环境配置
  celery-worker:
    restart: "no"
    volumes:
      - ./:/app  # 开发环境：挂载源码
    # 开发环境减少并发数，增加日志详细度
    command: ["celery", "-A", "celery_worker.celery_app", "worker",
              "--loglevel=debug",
              "--pool=prefork",
              "--concurrency=2",  # 开发环境减少并发
              "--heartbeat-interval=30"]

  # 🎨 前端开发容器 - 支持热重载
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: visa_automator_frontend_dev
    ports:
      - "5173:5173"  # Vite开发服务器端口
    volumes:
      - ./frontend:/app  # 挂载前端源码实现热重载
      - /app/node_modules  # 排除node_modules避免权限问题
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000/api  # 开发环境API地址
    restart: "no"
    networks:
      - app-network
    depends_on:
      - visa-automator

# 🔧 开发环境网络配置
networks:
  app-network:
    # 开发环境使用本地网络
    external: false
