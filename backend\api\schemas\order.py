"""
订单API模型 - 纯Pydantic模型
============================

分离关注点：仅包含API请求/响应模型
不包含SQLAlchemy ORM模型
用于FastAPI路由的数据验证和序列化
"""

from datetime import date, datetime
from typing import Any
import uuid

from pydantic import BaseModel, Field, field_validator

from backend.models.order import OrderStatus


class CreateOrderRequest(BaseModel):
    """创建订单请求"""

    applicant_name: str = Field(
        ..., min_length=1, max_length=100, description="申请人姓名"
    )
    passport_number: str = Field(
        ..., min_length=6, max_length=20, description="护照号码"
    )
    date_of_birth: date = Field(..., description="出生日期")

    # 完整申请数据
    application_data: dict[str, Any] = Field(..., description="完整的申请表单数据")

    @field_validator("passport_number")
    @classmethod
    def validate_passport_number(cls, v):
        if not v.strip():
            raise ValueError("护照号码不能为空")
        return v.strip().upper()

    @field_validator("applicant_name")
    @classmethod
    def validate_applicant_name(cls, v):
        if not v.strip():
            raise ValueError("申请人姓名不能为空")
        return v.strip()


class CreateOrderResponse(BaseModel):
    """创建订单响应"""

    success: bool
    data: dict[str, Any] | None = None
    message: str | None = None


class OrderInfo(BaseModel):
    """订单信息"""

    # 核心编号
    order_no: str
    application_number: str | None = None

    # 基本信息 - 修复：user_id使用UUID类型
    user_id: uuid.UUID
    applicant_name: str
    passport_number: str
    date_of_birth: date

    # 状态信息
    status: OrderStatus
    error_message: str | None = None
    last_error_at: datetime | None = None

    # 重试信息
    retry_count: int
    max_retry_count: int

    # 业务数据
    application_data: dict[str, Any]

    # 时间信息
    created_at: datetime
    updated_at: datetime
    submitted_at: datetime | None = None
    approved_at: datetime | None = None
    completed_at: datetime | None = None

    class Config:
        from_attributes = True


class OrderQueryParams(BaseModel):
    """订单查询参数"""

    page: int = Field(1, ge=1, description="页码")
    limit: int = Field(20, ge=1, le=100, description="每页数量")
    status: str | None = Field(None, description="状态筛选")
    order_no: str | None = None
    application_number: str | None = None
    applicant_name: str | None = None
    customer_source: str | None = None
    passport_number: str | None = None
    date_of_birth: date | None = None
    date_from: date | None = None
    date_to: date | None = None


class PaginationInfo(BaseModel):
    """分页信息"""

    current_page: int
    total_pages: int
    total_items: int
    has_prev: bool
    has_next: bool
    page_size: int


class OrderQueryResponse(BaseModel):
    """订单查询响应"""

    success: bool
    data: dict[str, Any] | None = None
    message: str | None = None


class OrderDetailResponse(BaseModel):
    """订单详情响应"""

    success: bool
    data: dict[str, Any] | None = None
    message: str | None = None


class UpdateOrderRequest(BaseModel):
    """订单更新请求"""

    application_number: str | None = None
    status: OrderStatus | None = None
    error_message: str | None = None


# 明确导出所有需要的类
__all__ = [
    "OrderStatus",
    "CreateOrderRequest",
    "CreateOrderResponse",
    "OrderInfo",
    "OrderQueryParams",
    "PaginationInfo",
    "OrderQueryResponse",
    "OrderDetailResponse",
    "UpdateOrderRequest",
]
