"""
订单管理API路由

提供订单编号生成、查询、状态管理等完整API接口
严格按照L Notepad要求实现安全性和业务规范
"""

from datetime import date
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.security import HTTPBearer

from app.services.order_service import OrderService
from backend.api.schemas.order import (
    CreateOrderRequest,
    CreateOrderResponse,
    OrderQueryParams,
    UpdateOrderRequest,
)
from backend.auth_fastapi_users.dependencies import get_current_user
from backend.dependencies import get_order_service  # 使用统一依赖注入
from backend.models.order import OrderStatus

router = APIRouter(prefix="/visa/orders", tags=["订单管理"])
security = HTTPBearer()

# 移除全局订单服务实例，使用依赖注入


async def get_current_user_id(current_user=Depends(get_current_user)) -> UUID:
    """获取当前用户ID"""
    if not current_user:
        raise HTTPException(status_code=401, detail="用户未认证")
    return current_user.id


@router.post("/create", response_model=CreateOrderResponse)
async def create_order(
    request: CreateOrderRequest,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    创建新订单

    实现核心要求：
    - 原子性：编号生成和数据库写入在同一事务
    - 幂等性：防止短时间内重复请求（1秒内），允许用户多次申请签证
    - 安全性：编号不可推算，只能由后端生成

    注意：同一用户可以为同一护照多次申请签证（业务需求），
    幂等性仅防止网络重复提交，不限制正常的重复申请。
    """
    try:
        result = await service.create_order(current_user_id, request)

        if result["success"]:
            return CreateOrderResponse(
                success=True, data=result["data"], message=result["message"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建订单失败: {str(e)}")


@router.get("/query")
async def query_orders(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    status: str | None = Query(None, description="订单状态"),
    order_no: str | None = Query(None, description="订单编号（支持模糊查询）"),
    application_number: str | None = Query(
        None, description="申请编号（支持模糊查询）"
    ),
    applicant_name: str | None = Query(None, description="申请人姓名（支持模糊查询）"),
    customer_source: str | None = Query(None, description="客户来源（支持模糊查询）"),
    date_from: date | None = Query(None, description="开始日期"),
    date_to: date | None = Query(None, description="结束日期"),
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    查询订单列表

    安全性：只能查询当前用户的订单
    支持多条件筛选和分页
    """
    try:
        # 构建查询参数
        params = OrderQueryParams(
            page=page,
            limit=limit,
            status=status,  # 直接传递字符串，让repository判断是订单状态还是签证状态
            order_no=order_no,
            application_number=application_number,  # 恢复application_number字段处理
            applicant_name=applicant_name,  # 🔧 修复：新增申请人姓名筛选
            customer_source=customer_source,  # 🔧 修复：新增客户来源筛选
            date_from=date_from,
            date_to=date_to,
        )

        result = await service.query_user_orders(current_user_id, params)

        if result["success"]:
            return {"success": True, "data": result["data"], "message": "查询成功"}
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询订单失败: {str(e)}")


@router.get("/{order_no}/detail")
async def get_order_detail(
    order_no: str,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    获取订单详情

    安全性：用户ID + 订单编号双重校验
    包含完整订单信息和状态变更历史
    """
    try:
        result = await service.get_order_detail(current_user_id, order_no)

        if result["success"]:
            return {
                "success": True,
                "data": result["data"],
                "message": "获取订单详情成功",
            }
        else:
            raise HTTPException(status_code=404, detail=result["message"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单详情失败: {str(e)}")


@router.put("/{order_no}/update")
async def update_order(
    order_no: str,
    request: UpdateOrderRequest,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    更新订单信息

    主要用于更新越南官方编号、状态等
    安全性：用户ID + 订单编号双重校验
    """
    try:
        # 如果请求中包含状态更新
        if request.status:
            result = await service.update_order_status(
                user_id=current_user_id,
                order_no=order_no,
                new_status=request.status,
                reason="API状态更新",
                operator_type="user",  # 用户主动更新
            )
        else:
            # TODO: 实现其他字段更新（如application_number等）
            raise HTTPException(status_code=400, detail="暂不支持的更新操作")

        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新订单失败: {str(e)}")


@router.post("/{order_no}/retry")
async def retry_order(
    order_no: str,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    重试订单处理

    用于失败订单的重新处理
    只有failed状态的订单可以重试
    """
    try:
        # 先获取订单详情，检查是否可以重试
        order_detail = await service.get_order_detail(current_user_id, order_no)

        if not order_detail["success"]:
            raise HTTPException(status_code=404, detail=order_detail["message"])

        order = order_detail["data"]["order"]

        if order["status"] != "failed":
            raise HTTPException(status_code=400, detail="只有失败状态的订单可以重试")

        if order["retry_count"] >= order["max_retry_count"]:
            raise HTTPException(status_code=400, detail="已达到最大重试次数")

        # 更新状态为created，准备重新处理
        result = await service.update_order_status(
            user_id=current_user_id,
            order_no=order_no,
            new_status=OrderStatus.CREATED,
            reason="用户手动重试",
            operator_type="user",
        )

        if result["success"]:
            return {"success": True, "message": "重试请求已提交，订单将重新处理"}
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重试订单失败: {str(e)}")


@router.get("/by-application/{application_number}")
async def get_order_by_application_number(
    application_number: str,
    current_user_id: UUID = Depends(get_current_user_id),  # type: ignore[assignment]
    service: OrderService = Depends(get_order_service),
):
    """
    根据越南官方编号查询订单

    用于兼容性查询和状态同步
    安全性：只能查询当前用户的订单
    """
    try:
        # 通过查询参数搜索
        params = OrderQueryParams(
            page=1,
            limit=1,
            status=None,  # 添加明确的status参数
            application_number=application_number,
        )

        result = await service.query_user_orders(current_user_id, params)

        if result["success"] and result["data"]["orders"]:
            order = result["data"]["orders"][0]
            return {"success": True, "data": {"order": order}, "message": "查询成功"}
        else:
            raise HTTPException(status_code=404, detail="未找到对应的订单")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询订单失败: {str(e)}")


@router.get("/{order_no}/history")
async def get_order_status_history(
    order_no: str,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    获取订单状态历史

    返回订单的完整状态变更记录
    """
    try:
        result = await service.get_order_detail(current_user_id, order_no)

        if result["success"]:
            return {
                "success": True,
                "data": result["data"]["status_history"],
                "message": "获取状态历史成功",
            }
        else:
            raise HTTPException(status_code=404, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取状态历史失败: {str(e)}")


@router.post("/{order_no}/cancel")
async def cancel_order(
    order_no: str,
    request: dict[str, Any] | None = None,
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """
    取消订单

    仅限created状态的订单可以取消
    """
    if request is None:
        request = {}
    try:
        # 先获取订单详情，检查是否可以取消
        order_detail = await service.get_order_detail(current_user_id, order_no)

        if not order_detail["success"]:
            raise HTTPException(status_code=404, detail=order_detail["message"])

        order = order_detail["data"]["order"]

        if order["status"] != "created":
            raise HTTPException(status_code=400, detail="只有created状态的订单可以取消")

        # 更新状态为cancelled
        result = await service.update_order_status(
            user_id=current_user_id,
            order_no=order_no,
            new_status=OrderStatus.CANCELLED,
            reason=request.get("reason", "用户取消订单"),
            operator_type="user",
        )

        if result["success"]:
            return {"success": True, "message": "订单已取消"}
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消订单失败: {str(e)}")


# 健康检查和统计接口
@router.get(
    "/stats/summary",
    summary="获取订单统计信息",
    description="获取用户订单统计信息，包括各状态订单数量",
)
async def get_order_stats(
    current_user_id: UUID = Depends(get_current_user_id),
    service: OrderService = Depends(get_order_service),
):
    """获取订单统计信息"""
    try:
        result = await service.get_order_stats(user_id=current_user_id)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单统计失败: {str(e)}")


@router.on_event("shutdown")
async def shutdown_order_service():
    """应用关闭时清理资源"""
    # 注意：在依赖注入模式中，不需要手动管理service生命周期
    # 资源清理由FastAPI的依赖注入系统自动处理
    pass
