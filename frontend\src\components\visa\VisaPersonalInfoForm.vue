<template>
  <el-card class="form-section" shadow="hover">
    <template #header>
      <div class="section-header">
        <el-icon><User /></el-icon>
        <span class="title">Personal Information 个人信息</span>
      </div>
    </template>

    <!-- 个人基本信息 - 每行4个字段 -->
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="姓氏" prop="personalInfo.surname" required>
          <el-input
            v-model="personalInfo.surname"
            placeholder="护照上的姓氏"
            :class="
              getFieldClass ? getFieldClass('personalInfo.surname', personalInfo.surname) : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="名字" prop="personalInfo.given_name" required>
          <el-input
            v-model="personalInfo.given_name"
            placeholder="护照上的名字"
            :class="
              getFieldClass ? getFieldClass('personalInfo.given_name', personalInfo.given_name) : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="中文名" prop="personalInfo.chinese_name">
          <el-input v-model="personalInfo.chinese_name" placeholder="中文名(可选)" />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="性别" prop="personalInfo.sex" required>
          <el-radio-group v-model="personalInfo.sex" class="custom-radio-group">
            <el-radio value="M">男</el-radio>
            <el-radio value="F">女</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第二行 - 出生信息和国籍 -->
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="出生日期" prop="personalInfo.dob" required>
          <el-date-picker
            v-model="personalInfo.dob"
            type="date"
            placeholder="选择出生日期"
            format="DD/MM/YYYY"
            value-format="DD/MM/YYYY"
            style="width: 100%"
            :class="getFieldClass ? getFieldClass('personalInfo.dob', personalInfo.dob) : ''"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="出生地" prop="personalInfo.place_of_birth" required>
          <el-input
            v-model="personalInfo.place_of_birth"
            placeholder="出生地"
            :class="
              getFieldClass
                ? getFieldClass('personalInfo.place_of_birth', personalInfo.place_of_birth)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="国籍" prop="personalInfo.nationality" required>
          <el-input
            v-model="personalInfo.nationality"
            placeholder="国籍"
            :class="
              getFieldClass
                ? getFieldClass('personalInfo.nationality', personalInfo.nationality)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="宗教" prop="personalInfo.religion" required>
          <el-input
            v-model="personalInfo.religion"
            placeholder="宗教"
            :class="
              getFieldClass ? getFieldClass('personalInfo.religion', personalInfo.religion) : ''
            "
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第三行 - 护照信息 -->
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="护照号码" prop="passportInfo.passport_number" required>
          <el-input
            v-model="passportInfo.passport_number"
            placeholder="护照号码"
            :class="
              getFieldClass
                ? getFieldClass('passportInfo.passport_number', passportInfo.passport_number)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="签发日期" prop="passportInfo.date_of_issue" required>
          <el-date-picker
            v-model="passportInfo.date_of_issue"
            type="date"
            placeholder="选择签发日期"
            format="DD/MM/YYYY"
            value-format="DD/MM/YYYY"
            style="width: 100%"
            :class="
              getFieldClass
                ? getFieldClass('passportInfo.date_of_issue', passportInfo.date_of_issue)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="签发地" prop="passportInfo.place_of_issue" required>
          <el-input
            v-model="passportInfo.place_of_issue"
            placeholder="签发地"
            :class="
              getFieldClass
                ? getFieldClass('passportInfo.place_of_issue', passportInfo.place_of_issue)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="护照有效期" prop="passportInfo.passport_expiry" required>
          <el-date-picker
            v-model="passportInfo.passport_expiry"
            type="date"
            placeholder="选择有效期"
            format="DD/MM/YYYY"
            value-format="DD/MM/YYYY"
            style="width: 100%"
            :class="
              getFieldClass
                ? getFieldClass('passportInfo.passport_expiry', passportInfo.passport_expiry)
                : ''
            "
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第四行 - 联系信息 -->
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="邮箱" prop="contactInfo.email" required>
          <el-input
            v-model="contactInfo.email"
            type="email"
            placeholder="邮箱地址"
            :class="getFieldClass ? getFieldClass('contactInfo.email', contactInfo.email) : ''"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="电话号码" prop="contactInfo.telephone_number" required>
          <el-input
            v-model="contactInfo.telephone_number"
            placeholder="电话号码"
            :class="
              getFieldClass
                ? getFieldClass('contactInfo.telephone_number', contactInfo.telephone_number)
                : ''
            "
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="永久地址" prop="contactInfo.permanent_address">
          <el-input v-model="contactInfo.permanent_address" placeholder="永久地址(可选)" />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-form-item label="联系地址" prop="contactInfo.contact_address">
          <el-input v-model="contactInfo.contact_address" placeholder="联系地址(可选)" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 第五行 - 紧急联系人信息 -->
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-form-item label="紧急联系人" prop="contactInfo.emergency_contact_name">
          <el-input v-model="contactInfo.emergency_contact_name" placeholder="紧急联系人(可选)" />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-form-item label="紧急联系电话" prop="contactInfo.emergency_contact_phone">
          <el-input
            v-model="contactInfo.emergency_contact_phone"
            placeholder="紧急联系电话(可选)"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-form-item label="紧急联系地址" prop="contactInfo.emergency_address">
          <el-input v-model="contactInfo.emergency_address" placeholder="紧急地址(可选)" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import type { ContactInfo, PassportInfo, PersonalInfo } from '@/types/form'
import { User } from '@element-plus/icons-vue'
import { nextTick, watch } from 'vue'

// Props - 只保留函数，移除数据对象定义以避免与defineModel冲突
interface Props {
  getFieldClass?: (fieldPath: string, value: unknown) => { [key: string]: boolean }
}

// Emits - 发出验证事件
interface Emits {
  (e: 'validate-personal', isValid: boolean): void
  (e: 'validate-passport', isValid: boolean): void
  (e: 'validate-contact', isValid: boolean): void
}

// v-model 绑定 - 正确的数据绑定方式
const personalInfo = defineModel<PersonalInfo>('personalInfo', { required: true })
const passportInfo = defineModel<PassportInfo>('passportInfo', { required: true })
const contactInfo = defineModel<ContactInfo>('contactInfo', { required: true })

const { getFieldClass } = defineProps<Props>()
const emit = defineEmits<Emits>()

// 验证个人信息
const validatePersonalInfo = () => {
  const isValid = !!(
    personalInfo.value.surname &&
    personalInfo.value.given_name &&
    personalInfo.value.sex &&
    personalInfo.value.dob &&
    personalInfo.value.place_of_birth &&
    personalInfo.value.nationality &&
    personalInfo.value.religion
  )
  emit('validate-personal', isValid)
  return isValid
}

// 验证护照信息
const validatePassportInfo = () => {
  const isValid = !!(
    passportInfo.value.passport_number &&
    passportInfo.value.date_of_issue &&
    passportInfo.value.place_of_issue &&
    passportInfo.value.passport_expiry
  )
  emit('validate-passport', isValid)
  return isValid
}

// 验证联系信息
const validateContactInfo = () => {
  const isValid = !!(contactInfo.value.email && contactInfo.value.telephone_number)
  emit('validate-contact', isValid)
  return isValid
}

// 监听数据变化并触发验证
watch(
  personalInfo,
  () => {
    nextTick(() => validatePersonalInfo())
  },
  { deep: true, immediate: true },
)

watch(
  passportInfo,
  () => {
    nextTick(() => validatePassportInfo())
  },
  { deep: true, immediate: true },
)

watch(
  contactInfo,
  () => {
    nextTick(() => validateContactInfo())
  },
  { deep: true, immediate: true },
)
</script>

<style scoped lang="scss">
.form-section {
  margin-bottom: 24px;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

// 输入框样式优化
:deep(.el-form-item) {
  .el-input {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 2px 8px #f0f1f2;
      transition: all 0.3s ease;
      border: 1px solid var(--el-border-color);

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-1px);
      }

      &.is-focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        transform: translateY(-1px);
      }
    }
  }

  // 日期选择器特殊样式
  .el-date-editor {
    .el-input__wrapper {
      border-radius: 8px;
      box-shadow: 0 2px 8px #f0f1f2;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-1px);
      }
    }

    &.is-focus .el-input__wrapper {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  // 校验成功状态 - 绿色勾
  .field-filled .el-input__wrapper {
    border-color: var(--el-color-success);
    position: relative;

    &::after {
      content: '✓';
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--el-color-success);
      font-weight: bold;
      font-size: 16px;
      z-index: 10;
    }
  }

  // 校验错误状态 - 红色边框
  .field-empty .el-input__wrapper {
    border-color: var(--el-color-danger);
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);

    &:hover {
      border-color: var(--el-color-danger);
      box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3);
    }
  }
}

// 自定义单选按钮样式 - 统一样式
.custom-radio-group {
  display: flex;
  gap: 16px;

  :deep(.el-radio) {
    margin-right: 0;

    .el-radio__input {
      &.is-checked {
        .el-radio__inner {
          width: 18px;
          height: 18px;
          background-color: var(--el-color-primary);
          border-color: var(--el-color-primary);

          &::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(15deg);
            font-size: 12px;
            font-weight: bold;
            color: white;
            line-height: 1;
            width: auto;
            height: auto;
            border-radius: 0;
            background: transparent;
          }
        }
      }
    }

    .el-radio__inner {
      width: 18px;
      height: 18px;
      border: 2px solid var(--el-border-color);
      border-radius: 50%;
      background-color: white;
      transition: all 0.3s ease;

      &::after {
        // 隐藏默认的实心圆点
        background: transparent;
        border: none;
      }

      &:hover {
        border-color: var(--el-color-primary);
        background-color: rgba(64, 158, 255, 0.1);
        transform: scale(1.05);
      }
    }

    &:hover {
      .el-radio__inner {
        border-color: var(--el-color-primary);
        background-color: rgba(64, 158, 255, 0.1);
        transform: scale(1.05);
      }

      .el-radio__label {
        color: var(--el-color-primary);
      }
    }

    &.is-checked {
      .el-radio__inner {
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }
      }

      .el-radio__label {
        color: var(--el-color-primary);
        font-weight: 600;
      }
    }

    .el-radio__label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      padding-left: 10px;
      font-weight: 500;
    }
  }
}
</style>
