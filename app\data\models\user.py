import uuid

from sqlalchemy import <PERSON>ole<PERSON>, Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class User(Base):
    __tablename__ = "user"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="用户主键"
    )
    username = Column(String(64), unique=True, nullable=False, doc="用户名")
    email = Column(String(128), unique=True, nullable=False, doc="邮箱")
    hashed_password = Column(
        String(128), nullable=False, doc="密码哈希（FastAPI Users标准字段）"
    )
    company_name = Column(String(128), doc="公司名称")
    company_address = Column(String(256), doc="公司地址")
    company_contacts = Column(String(64), doc="公司联系人")
    phone = Column(String(32), doc="联系电话")  # 统一使用phone字段
    is_active = Column(Boolean, default=True, doc="是否激活")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="更新时间",
    )
    role = Column(String(32), default="user", nullable=False, doc="用户角色")
    is_superuser = Column(Boolean, default=False, doc="是否超级管理员")
    is_verified = Column(Boolean, default=False, doc="是否已验证")

    # FastAPI Users兼容性字段已在上面定义为hashed_password Column

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, username={self.username}, role={self.role})>"
