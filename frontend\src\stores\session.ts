/**
 * 🎯 简化的两层检测机制
 *
 * 设计原则：保持简单高效
 * 1. 🎯 页面聚焦时立即检测（零开销，体验最佳）
 * 2. ⚡ 用户操作时立即检测（零开销，现有机制）
 *
 * 移除：心跳检测、手动检测（用户要求简化）
 * 优势：极简代码，零资源消耗，检测及时
 */

import { api } from '@/api/request'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSessionStore = defineStore('session', () => {
  // 🔥 核心状态
  const isSessionInvalidated = ref(false)
  const sessionInvalidationMessage = ref<string>('')

  // 🔧 内部状态
  const focusListenersAttached = ref(false)
  const isLoggingOut = ref(false) // 新增：退出登录标记

  // 📊 统计信息（可选，用于监控）
  const checkStats = ref({
    totalChecks: 0,
    focusChecks: 0,
    operationChecks: 0,
    invalidationDetected: 0,
  })

  // 🔧 事件处理器引用，用于移除监听器（修复内存泄漏）
  let handleFocusRef: (() => void) | null = null
  let handleVisibilityChangeRef: (() => void) | null = null

  /**
   * 🔍 核心会话检测函数
   */
  const checkSession = async (source: 'focus' | 'operation' = 'operation'): Promise<boolean> => {
    // 避免在登录页面或退出登录时检测
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      return false
    }

    if (isLoggingOut.value) {
      console.log('🚪 正在退出登录，跳过会话检测')
      return false
    }

    try {
      checkStats.value.totalChecks++
      if (source === 'focus') checkStats.value.focusChecks++
      if (source === 'operation') checkStats.value.operationChecks++

      const response = await api.get<Record<string, unknown>>('/api/session-status')

      if (response && response.success) {
        console.log(`✅ 会话检查通过 (${source})`)
        return true
      } else {
        console.log(`❌ 会话检查失败 (${source})`)
        return false
      }
    } catch (error: unknown) {
      // 检查是否为HTTP 401未授权错误
      if (
        typeof error === 'object' &&
        error !== null &&
        (error as { response?: { status?: number } }).response?.status === 401
      ) {
        console.log(`⚠️ 检测到401未授权 (${source})，触发会话失效`)
        setSessionInvalidated('您的登录状态已过期，请重新登录')
      } else {
        // 其他错误
        console.log(`⚠️ 会话检查异常 (${source}):`, error)
      }
      return false
    }
  }

  /**
   * 🔥 设置会话失效状态（由API拦截器调用）
   */
  const setSessionInvalidated = (message?: string) => {
    // 如果正在退出登录，不处理会话失效
    if (isLoggingOut.value) {
      console.log('🚪 正在退出登录，忽略会话失效提醒')
      return
    }

    console.log('🔥 检测到会话失效，设置状态让App.vue统一处理UI')
    isSessionInvalidated.value = true
    sessionInvalidationMessage.value = message ?? '您的账户已在其他设备登录，请重新登录'
    checkStats.value.invalidationDetected++

    // 🔧 修复：移除立即显示对话框，让App.vue统一处理UI
    // showSessionInvalidatedDialog()

    // 同时设置authStore状态，触发App.vue监听器
    ;(async () => {
      try {
        const { useAuthStore } = await import('@/stores/auth')
        const authStore = useAuthStore()
        authStore.setSessionInvalidated()
      } catch (error) {
        console.error('设置authStore会话失效状态失败:', error)
      }
    })()

    // 停止检测机制
    stopAllChecking()
  }

  /**
   * 🎯 页面聚焦检测（第一层：零开销，体验最佳）
   */
  const setupFocusDetection = () => {
    if (focusListenersAttached.value || typeof window === 'undefined') return

    const handleFocus = () => {
      console.log('👁️ 页面聚焦，执行会话检测')
      checkSession('focus')
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('👁️ 页面可见，执行会话检测')
        checkSession('focus')
      }
    }

    // 🔧 确保事件监听器正确绑定
    try {
      window.addEventListener('focus', handleFocus, { passive: true })
      document.addEventListener('visibilitychange', handleVisibilityChange, { passive: true })

      focusListenersAttached.value = true
      console.log('✅ 页面聚焦检测已启用')

      // 🔧 保存事件处理器引用，用于移除监听器（修复内存泄漏）
      handleFocusRef = handleFocus
      handleVisibilityChangeRef = handleVisibilityChange
    } catch (error) {
      console.error('❌ 页面聚焦检测启用失败:', error)
    }
  }

  /**
   * 🚀 启动两层检测机制
   */
  const startSessionDetection = () => {
    console.log('🚀 启动两层会话检测机制（简化版）')

    // 第一层：页面聚焦检测
    console.log('📍 启动第一层：页面聚焦检测')
    setupFocusDetection()

    // 第二层：用户操作检测（API拦截器自动处理）
    console.log('📍 第二层：用户操作检测（API拦截器自动处理）')

    console.log('✅ 两层检测机制启动完成!')
    console.log('🎯 检测策略：')
    console.log('  1. 页面聚焦时立即检测')
    console.log('  2. 任何API操作时立即检测')
  }

  /**
   * 🛑 停止所有检测
   */
  const stopAllChecking = () => {
    // 移除页面聚焦监听器
    if (focusListenersAttached.value && typeof window !== 'undefined') {
      // 注意：实际应用中需要保存监听器引用以正确移除
      // 这里简化处理，因为页面即将重定向
      focusListenersAttached.value = false

      // 🔧 移除事件监听器
      if (handleFocusRef) {
        window.removeEventListener('focus', handleFocusRef)
        handleFocusRef = null
      }
      if (handleVisibilityChangeRef) {
        document.removeEventListener('visibilitychange', handleVisibilityChangeRef)
        handleVisibilityChangeRef = null
      }
    }

    console.log('🛑 所有会话检测已停止')
  }

  /**
   * 🚪 设置退出登录状态
   */
  const setLoggingOut = (value: boolean) => {
    isLoggingOut.value = value
    console.log(`🚪 退出登录状态: ${value}`)
  }

  /**
   * 🧹 清除会话失效状态
   */
  const clearSessionInvalidated = () => {
    isSessionInvalidated.value = false
    sessionInvalidationMessage.value = ''
  }

  /**
   * 🔄 重置所有状态（用于登出）
   */
  const reset = () => {
    clearSessionInvalidated()
    stopAllChecking()
    setLoggingOut(false)
    checkStats.value = {
      totalChecks: 0,
      focusChecks: 0,
      operationChecks: 0,
      invalidationDetected: 0,
    }
    console.log('🔄 会话存储已重置')
  }

  /**
   * 📊 获取检测统计信息
   */
  const getCheckStats = () => ({
    ...checkStats.value,
    focusDetectionEnabled: focusListenersAttached.value,
    isLoggingOut: isLoggingOut.value,
  })

  return {
    // 🔥 核心状态
    isSessionInvalidated,
    sessionInvalidationMessage,

    // 🔧 核心方法
    setSessionInvalidated,
    clearSessionInvalidated,
    startSessionDetection,
    stopAllChecking,
    setLoggingOut,
    reset,

    // 📊 工具方法
    checkSession,
    getCheckStats,
  }
})
