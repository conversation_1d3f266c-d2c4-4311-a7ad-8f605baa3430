"""
带会话验证的认证依赖
在JWT验证基础上，增加Redis会话验证，实现单设备登录
"""

from fastapi import Depends, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi_users.db import SQLAlchemyUserDatabase

from backend.auth_fastapi_users.database import get_user_db
from backend.auth_fastapi_users.models import User
from backend.utils.single_device_session import session_manager

security = HTTPBearer()


async def get_current_user_with_session_validation(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_db: SQLAlchemyUserDatabase = Depends(get_user_db),
) -> User:
    """
    获取当前用户（带会话验证）
    两步验证：1. JWT验证 2. Redis会话验证
    """
    try:
        import jwt

        from backend.config.settings import settings

        # 第一步：JWT验证
        try:
            payload = jwt.decode(
                credentials.credentials,
                settings.secret_key,
                algorithms=["HS256"],
                audience=["fastapi-users:auth"],
            )
            user_id = payload.get("sub")
            if not user_id:
                raise HTTPException(status_code=401, detail="Invalid token")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")

        # 获取用户信息
        user = await user_db.get(user_id)
        if user is None or not user.is_active:
            raise HTTPException(status_code=401, detail="User not found or inactive")

        # 第二步：Redis会话验证（异步）
        print(f"🔍 开始Redis会话验证，用户ID: {user_id}")
        if not await session_manager.validate_user_token(
            user_id, credentials.credentials
        ):
            # 会话失效，返回特定错误
            print(f"❌ 用户{user_id}会话验证失败，触发SESSION_INVALIDATED")
            raise HTTPException(
                status_code=401,
                detail={
                    "error": "SESSION_INVALIDATED",
                    "message": "您的账户已在其他设备登录，请重新登录。如果不是本人操作，请立即修改密码。",
                },
            )

        print(f"✅ 用户{user_id}会话验证成功")

        return user

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 会话验证失败: {e}")
        raise HTTPException(status_code=401, detail="Authentication failed")


# 导出依赖函数 - 现在是正确的FastAPI依赖
current_user_with_session = get_current_user_with_session_validation
