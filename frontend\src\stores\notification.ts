import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

/**
 * 全局通知状态管理
 * 基于authStore的成功模式，提供统一的通知状态管理
 *
 * 设计原则：
 * 1. 业务逻辑层只调用store方法，不知道UI实现
 * 2. UI层监听状态变化并显示通知
 * 3. 职责分离：store管状态，组件管显示
 */
export const useNotificationStore = defineStore('notification', () => {
  // 通知状态 - 复用authStore的成功模式
  const lastError = ref<string | null>(null)
  const lastSuccess = ref<string | null>(null)
  const lastWarning = ref<string | null>(null)
  const lastInfo = ref<string | null>(null)

  // 显示通知方法 - 业务逻辑层调用这些方法
  const showError = (message: string) => {
    lastError.value = message
  }

  const showSuccess = (message: string) => {
    lastSuccess.value = message
  }

  const showWarning = (message: string) => {
    lastWarning.value = message
  }

  const showInfo = (message: string) => {
    lastInfo.value = message
  }

  // 清除消息状态
  const clearMessages = () => {
    lastError.value = null
    lastSuccess.value = null
    lastWarning.value = null
    lastInfo.value = null
  }

  // 清除特定类型的消息
  const clearError = () => {
    lastError.value = null
  }
  const clearSuccess = () => {
    lastSuccess.value = null
  }
  const clearWarning = () => {
    lastWarning.value = null
  }
  const clearInfo = () => {
    lastInfo.value = null
  }

  // 业务特定的便利方法
  const showAuthError = (message: string = '登录已过期，请重新登录') => {
    showError(message)
  }

  // 🔥 会话失效专用方法（单设备登录）
  const showSessionInvalidated = (message: string = '您的账户已在其他设备登录，请重新登录') => {
    showWarning(message) // 使用警告级别，区别于普通错误
  }

  const showNetworkError = (message: string = '网络请求失败，请检查网络连接') => {
    showError(message)
  }

  const showValidationError = (message: string) => {
    showError(message)
  }

  const showOperationSuccess = (message: string) => {
    showSuccess(message)
  }

  // 复制操作的便利方法
  const showCopySuccess = (item: string) => {
    showSuccess(`${item}已复制`)
  }

  const showCopyError = () => {
    showError('复制失败')
  }

  // 🔧 移除：确认对话框不应该在 store 层处理
  // 确认对话框应该由组件层直接使用 ElMessageBox 处理

  return {
    // 状态（只读）
    lastError: computed(() => lastError.value),
    lastSuccess: computed(() => lastSuccess.value),
    lastWarning: computed(() => lastWarning.value),
    lastInfo: computed(() => lastInfo.value),

    // 基础方法
    showError,
    showSuccess,
    showWarning,
    showInfo,
    clearMessages,
    clearError,
    clearSuccess,
    clearWarning,
    clearInfo,

    // 业务便利方法
    showAuthError,
    showSessionInvalidated,
    showNetworkError,
    showValidationError,
    showOperationSuccess,
    showCopySuccess,
    showCopyError,

    // 🔧 移除：确认对话框由组件层处理
  }
})
