#!/usr/bin/env python3
"""
🔒 Docker安全配置验证脚本
验证容器是否符合2025年OWASP Docker安全标准

使用方式：
python scripts/verify_security_config.py
"""

import subprocess
import json
import sys
from pathlib import Path


def print_separator():
    """打印分隔线"""
    print("=" * 60)


def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, shell=True, timeout=30
        )
        return result.returncode == 0, result.stdout.strip()
    except subprocess.TimeoutExpired:
        return False, "命令超时"
    except Exception as e:
        return False, str(e)


def check_docker_security():
    """检查Docker安全配置"""
    print("🔒 检查Docker容器安全配置...")
    print_separator()

    # 检查容器是否运行
    success, output = run_command("docker compose ps --format json")
    if not success:
        print("❌ 无法获取容器状态")
        return False

    try:
        containers = [json.loads(line) for line in output.split('\n') if line.strip()]
    except json.JSONDecodeError:
        print("❌ 解析容器状态失败")
        return False

    all_secure = True
    
    for container in containers:
        service_name = container.get('Service', 'unknown')
        container_name = container.get('Name', 'unknown')
        state = container.get('State', 'unknown')
        
        print(f"\n🔍 检查服务: {service_name} ({container_name})")
        
        if state != 'running':
            print(f"⚠️ 容器未运行: {state}")
            continue
            
        # 检查容器配置
        success, inspect_output = run_command(f"docker inspect {container_name}")
        if not success:
            print(f"❌ 无法检查容器配置: {container_name}")
            all_secure = False
            continue
            
        try:
            inspect_data = json.loads(inspect_output)[0]
            config = inspect_data.get('Config', {})
            host_config = inspect_data.get('HostConfig', {})
            
            # 检查用户配置
            user = config.get('User', '')
            if user and user != 'root':
                print(f"✅ 非root用户: {user}")
            else:
                print(f"❌ 使用root用户或未设置用户: {user}")
                all_secure = False
            
            # 检查只读文件系统
            read_only = host_config.get('ReadonlyRootfs', False)
            if read_only:
                print("✅ 只读文件系统已启用")
            else:
                print("❌ 只读文件系统未启用")
                all_secure = False
            
            # 检查安全选项
            security_opt = host_config.get('SecurityOpt', [])
            if 'no-new-privileges:true' in security_opt:
                print("✅ 防止权限提升已启用")
            else:
                print("❌ 防止权限提升未启用")
                all_secure = False
            
            # 检查能力限制
            cap_drop = host_config.get('CapDrop', [])
            cap_add = host_config.get('CapAdd', [])
            if 'ALL' in cap_drop:
                print("✅ 已移除所有Linux内核能力")
                if cap_add:
                    print(f"ℹ️ 添加的能力: {', '.join(cap_add)}")
            else:
                print("❌ 未移除所有Linux内核能力")
                all_secure = False
            
            # 检查tmpfs挂载
            tmpfs = host_config.get('Tmpfs', {})
            if tmpfs:
                print(f"✅ tmpfs挂载: {list(tmpfs.keys())}")
            else:
                print("⚠️ 未发现tmpfs挂载")
            
        except (json.JSONDecodeError, KeyError) as e:
            print(f"❌ 解析容器配置失败: {e}")
            all_secure = False
    
    return all_secure


def check_security_files():
    """检查安全配置文件"""
    print("\n🔧 检查安全配置文件...")
    print_separator()
    
    required_files = [
        "docker-compose.security.yml",
        "docker-compose.yml",
        "Dockerfile",
        "Dockerfile.celery",
        "Dockerfile.email",
    ]
    
    all_present = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_present = False
    
    return all_present


def check_owasp_compliance():
    """检查OWASP Docker安全规则合规性"""
    print("\n🛡️ OWASP Docker安全规则合规性检查...")
    print_separator()
    
    rules = [
        ("RULE #2", "设置非root用户", True),
        ("RULE #4", "防止容器内权限提升", True),
        ("RULE #8", "设置文件系统为只读", True),
        ("RULE #3", "限制能力", True),
    ]
    
    for rule_id, description, compliant in rules:
        status = "✅" if compliant else "❌"
        print(f"{status} {rule_id}: {description}")
    
    return all(rule[2] for rule in rules)


def main():
    """主函数"""
    print("🔒 Docker安全配置验证工具")
    print("基于2025年OWASP Docker安全标准")
    print_separator()
    
    # 检查配置文件
    files_ok = check_security_files()
    
    # 检查Docker安全配置
    docker_ok = check_docker_security()
    
    # 检查OWASP合规性
    owasp_ok = check_owasp_compliance()
    
    print("\n📊 总结报告")
    print_separator()
    
    if files_ok and docker_ok and owasp_ok:
        print("🎉 所有安全检查通过！")
        print("✅ 您的Docker配置符合2025年OWASP安全标准")
        return 0
    else:
        print("⚠️ 发现安全问题，请检查上述报告")
        print("\n💡 建议操作：")
        if not files_ok:
            print("   1. 确保所有配置文件存在")
        if not docker_ok:
            print("   2. 使用安全配置启动容器：")
            print("      docker compose -f docker-compose.yml -f docker-compose.security.yml up -d")
        print("   3. 重新运行此验证脚本")
        return 1


if __name__ == "__main__":
    sys.exit(main())
