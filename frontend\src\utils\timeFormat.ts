/**
 * 统一时间格式化工具 - 第三阶段前端时区重构
 * 基于 date-fns/tz 专业时区处理库
 *
 * 核心原则：
 * - 后端统一提供UTC时间戳
 * - 前端负责本地化显示
 * - 使用专业库确保可靠性
 */

import { TZDate, tz } from '@date-fns/tz'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 默认时区配置 - 业务时区
 */
export const DEFAULT_TIMEZONE = 'Asia/Shanghai'

/**
 * 常用时间格式模板
 */
export const TIME_FORMATS = {
  DATETIME: 'yyyy-MM-dd HH:mm:ss',
  DATE_ONLY: 'yyyy-MM-dd',
  TIME_ONLY: 'HH:mm:ss',
  RELATIVE: 'MM月dd日 HH:mm',
  SHORT_DATE: 'MM-dd HH:mm',
} as const

/**
 * 统一的时间格式化函数
 * @param utcTimestamp UTC时间戳字符串或Date对象
 * @param formatType 时间格式类型
 * @param timezone 目标时区，默认为业务时区
 * @returns 格式化后的时间字符串
 */
export function formatDateTime(
  utcTimestamp: string | Date | null | undefined,
  formatType: keyof typeof TIME_FORMATS = 'DATETIME',
  timezone: string = DEFAULT_TIMEZONE,
): string {
  // 处理空值情况
  if (!utcTimestamp) {
    return '—'
  }

  try {
    // ✅ 修复：正确处理TZDate构造函数参数
    let tzDate: TZDate
    if (typeof utcTimestamp === 'string') {
      // 对于字符串，先转换为Date对象，再创建TZDate
      const tempDate = new Date(utcTimestamp)
      tzDate = new TZDate(tempDate.getTime(), timezone)
    } else {
      // 对于Date对象，直接使用getTime()
      tzDate = new TZDate(utcTimestamp.getTime(), timezone)
    }

    // 验证时间有效性
    if (isNaN(tzDate.getTime())) {
      console.warn('Invalid date:', utcTimestamp)
      return '无效时间'
    }

    // 使用date-fns格式化
    return format(tzDate, TIME_FORMATS[formatType], {
      locale: zhCN,
    })
  } catch (error) {
    console.error('Time formatting error:', error, utcTimestamp)
    return '时间格式错误'
  }
}

/**
 * 格式化日期（仅日期部分）
 */
export function formatDate(
  utcTimestamp: string | Date | null | undefined,
  timezone: string = DEFAULT_TIMEZONE,
): string {
  return formatDateTime(utcTimestamp, 'DATE_ONLY', timezone)
}

/**
 * 格式化时间（仅时间部分）
 */
export function formatTime(
  utcTimestamp: string | Date | null | undefined,
  timezone: string = DEFAULT_TIMEZONE,
): string {
  return formatDateTime(utcTimestamp, 'TIME_ONLY', timezone)
}

/**
 * 格式化相对时间（用于状态更新显示）
 */
export function formatRelativeTime(
  utcTimestamp: string | Date | null | undefined,
  timezone: string = DEFAULT_TIMEZONE,
): string {
  return formatDateTime(utcTimestamp, 'RELATIVE', timezone)
}

/**
 * 将本地日期时间转换为UTC时间戳（用于发送给后端）
 * @param localDateTime 本地日期时间
 * @param timezone 源时区，默认为业务时区
 * @returns UTC时间戳字符串
 */
export function toUTCTimestamp(
  localDateTime: string | Date,
  timezone: string = DEFAULT_TIMEZONE,
): string {
  try {
    // ✅ 修复：正确处理TZDate构造函数参数
    let tzDate: TZDate
    if (typeof localDateTime === 'string') {
      // 对于字符串，先转换为Date对象，再创建TZDate
      const tempDate = new Date(localDateTime)
      tzDate = new TZDate(tempDate.getTime(), timezone)
    } else {
      // 对于Date对象，直接使用getTime()
      tzDate = new TZDate(localDateTime.getTime(), timezone)
    }
    return tzDate.toISOString()
  } catch (error) {
    console.error('UTC conversion error:', error, localDateTime)
    throw new Error('无法转换为UTC时间')
  }
}

/**
 * 获取当前UTC时间戳
 */
export function getCurrentUTCTimestamp(): string {
  return new Date().toISOString()
}

/**
 * 时间范围验证工具
 */
export function validateTimeRange(
  startTime: string | Date | null,
  endTime: string | Date | null,
): { valid: boolean; message: string } {
  if (!startTime || !endTime) {
    return { valid: false, message: '开始时间和结束时间都必须提供' }
  }

  try {
    const start = new Date(startTime)
    const end = new Date(endTime)

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return { valid: false, message: '时间格式无效' }
    }

    if (start > end) {
      return { valid: false, message: '开始时间不能晚于结束时间' }
    }

    return { valid: true, message: '时间范围有效' }
  } catch {
    return { valid: false, message: '时间验证失败' }
  }
}

/**
 * 为date-fns函数提供时区上下文
 * 使用示例：isSameDay(date1, date2, { in: getTimezoneContext() })
 */
export function getTimezoneContext(timezone: string = DEFAULT_TIMEZONE) {
  return tz(timezone)
}
