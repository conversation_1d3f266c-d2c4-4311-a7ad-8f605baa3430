import { api } from '@/api/request'
import type { PreviousVisitInfo, VietnamContactInfo, VisaInfo } from '@/types/form'
import { ref } from 'vue'

// 字段错误状态管理
const fieldErrors = ref<Record<string, string>>({})

// 加急日期缓存数据结构
interface ExpeditedDateCache {
  date: string
  description: string
}

// API响应数据结构
interface ExpeditedDateResponse {
  success: boolean
  expedited_date: string
}

// 验证字段
export const useFormValidation = () => {
  // 加急日期缓存
  const expeditedDateCache = ref<Record<string, ExpeditedDateCache>>({
    '1days': { date: '', description: '' },
    '2days': { date: '', description: '' },
    '3days': { date: '', description: '' },
    '4days': { date: '', description: '' },
  })

  // 实时验证单个字段
  const validateFieldRealtime = (fieldName: string, value: unknown) => {
    // 这里可以添加具体的验证逻辑
    if (!value) {
      setFieldError(fieldName, '此字段为必填项')
      return false
    }

    clearFieldError(fieldName)
    return true
  }

  // 设置字段错误
  const setFieldError = (fieldName: string, error: string) => {
    fieldErrors.value[fieldName] = error
  }

  // 清除字段错误
  const clearFieldError = (fieldName: string) => {
    delete fieldErrors.value[fieldName]
  }

  // 获取字段错误
  const getFieldError = (fieldName: string): string => {
    return fieldErrors.value[fieldName] || ''
  }

  // 检查字段是否有错误
  const hasFieldError = (fieldName: string): boolean => {
    return !!fieldErrors.value[fieldName]
  }

  // 验证多个字段
  const validateFields = (data: Record<string, unknown>, requiredFields?: string[]) => {
    const errors: Record<string, string> = {}
    let isValid = true

    if (requiredFields) {
      for (const field of requiredFields) {
        if (!data[field]) {
          errors[field] = '此字段为必填项'
          isValid = false
        }
      }
    }

    return { isValid, errors }
  }

  // 验证日期格式 DD/MM/YYYY
  const isValidDate = (dateStr: string): boolean => {
    if (!dateStr) return false

    const regex = /^\d{1,2}\/\d{1,2}\/\d{4}$/
    if (!regex.test(dateStr)) return false

    const [day, month, year] = dateStr.split('/').map(Number)
    const date = new Date(year, month - 1, day)

    return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year
  }

  // 验证电子邮件格式
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 验证手机号码格式（支持多种格式）
  const isValidPhone = (phone: string): boolean => {
    // 移除所有空格和特殊字符，只保留数字和+号
    const cleaned = phone.replace(/[\s\-\(\)]/g, '')

    // 支持国际格式和国内格式
    const phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10,15}$/
    return phoneRegex.test(cleaned)
  }

  // 验证护照号码格式
  const isValidPassportNumber = (passport: string): boolean => {
    // 护照号码通常是6-12位字母数字组合（统一标准）
    const passportRegex = /^[A-Za-z0-9]{6,12}$/
    return passportRegex.test(passport.replace(/\s/g, ''))
  }

  // 日期选择器的禁用日期函数
  const disabledDate = (time: Date): boolean => {
    // 禁用过去的日期（包括今天之前的日期）
    const today = new Date()
    today.setHours(0, 0, 0, 0) // 设置为当天的开始时间
    return time.getTime() < today.getTime()
  }

  // 提取加急日期数据的工具函数
  const extractExpeditedDate = (response: unknown): ExpeditedDateResponse => {
    // API直接返回数据，不是嵌套在data字段中
    if (response && typeof response === 'object') {
      return response as ExpeditedDateResponse
    }
    return { success: false, expedited_date: '' }
  }

  // 加载出签生效日期
  const loadExpeditedDate = async (): Promise<void> => {
    try {
      // 获取1工作日日期
      const response1 = await api.get('/api/visa/calculate-expedited-date?days=1')
      const data1 = extractExpeditedDate(response1)

      if (data1.success && data1.expedited_date) {
        expeditedDateCache.value['1days'] = {
          date: data1.expedited_date,
          description: '',
        }
      }

      // 获取2工作日日期
      const response2 = await api.get('/api/visa/calculate-expedited-date?days=2')
      const data2 = extractExpeditedDate(response2)

      if (data2.success && data2.expedited_date) {
        expeditedDateCache.value['2days'] = {
          date: data2.expedited_date,
          description: '',
        }
      }

      // 获取3工作日日期
      const response3 = await api.get('/api/visa/calculate-expedited-date?days=3')
      const data3 = extractExpeditedDate(response3)

      if (data3.success && data3.expedited_date) {
        expeditedDateCache.value['3days'] = {
          date: data3.expedited_date,
          description: '',
        }
      }

      // 获取4工作日日期
      const response4 = await api.get('/api/visa/calculate-expedited-date?days=4')
      const data4 = extractExpeditedDate(response4)

      if (data4.success && data4.expedited_date) {
        expeditedDateCache.value['4days'] = {
          date: data4.expedited_date,
          description: '',
        }
      }
    } catch {
      // 静默处理错误，避免影响用户体验
    }
  }

  // 验证签证信息完整性
  const validateVisaInfo = (
    visaInfo: VisaInfo & PreviousVisitInfo,
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (!visaInfo.visa_entry_type) {
      errors.push('请选择签证类型')
    }

    if (!visaInfo.visa_validity_duration) {
      errors.push('请选择有效期')
    }

    if (!visaInfo.visa_start_date) {
      errors.push('请选择出签生效日期')
    }

    if (!visaInfo.intended_entry_gate) {
      errors.push('请选择预期入境口岸')
    }

    if (!visaInfo.purpose_of_entry) {
      errors.push('请选择入境目的')
    }

    // 如果选择了访问过越南，需要填写相关信息
    if (visaInfo.visited_vietnam_last_year) {
      if (!visaInfo.previous_entry_date) {
        errors.push('请填写上次入境日期')
      }
      if (!visaInfo.previous_exit_date) {
        errors.push('请填写上次出境日期')
      }
      if (!visaInfo.previous_purpose) {
        errors.push('请选择上次访问目的')
      }
    }

    const isValid = errors.length === 0
    return { isValid, errors }
  }

  // 处理日期输入框变化事件
  const handleDateChange = (visaInfo: VisaInfo & PreviousVisitInfo, value: string): void => {
    if (visaInfo.expedited_type && value) {
      // 检查手动输入的日期是否匹配当前选中的加急类型
      const expectedDate = expeditedDateCache.value[visaInfo.expedited_type]?.date

      if (expectedDate && value !== expectedDate) {
        // 日期不匹配，自动取消单选按钮选择
        visaInfo.expedited_type = undefined
      }
    }
  }

  // 处理加急类型变化
  const handleExpeditedChange = async (
    visaInfo: VisaInfo & PreviousVisitInfo,
    value: string,
  ): Promise<void> => {
    if (value === '1days' || value === '2days' || value === '3days' || value === '4days') {
      const cacheData = expeditedDateCache.value[value]

      if (!cacheData?.date) {
        await loadExpeditedDate()
      }

      const newCacheData = expeditedDateCache.value[value]

      if (newCacheData?.date) {
        visaInfo.visa_start_date = newCacheData.date
        // 🔧 修复：移除调试日志
      } else {
        // 🔧 修复：移除调试日志
      }
    }
  }

  // 处理近期访问变化
  const handleVisitedChange = (visaInfo: VisaInfo & PreviousVisitInfo): void => {
    // 如果改为"否"，清空相关字段
    if (!visaInfo.visited_vietnam_last_year) {
      visaInfo.previous_entry_date = ''
      visaInfo.previous_exit_date = ''
      visaInfo.previous_purpose = ''
    }
  }

  // 处理越南联系组织变化
  const handleVietnamContactChange = (
    visaInfo: VisaInfo & PreviousVisitInfo & VietnamContactInfo,
  ): void => {
    // 如果改为"否"，清空相关字段
    if (!visaInfo.has_vietnam_contact) {
      visaInfo.vietnam_contact_organization = ''
      visaInfo.vietnam_contact_phone = ''
      visaInfo.vietnam_contact_address = ''
      visaInfo.vietnam_contact_purpose = ''
    }
  }

  // 初始化加急日期缓存
  const initializeValidation = async (): Promise<void> => {
    await loadExpeditedDate()
  }

  return {
    fieldErrors,
    expeditedDateCache,
    validateFieldRealtime,
    setFieldError,
    clearFieldError,
    getFieldError,
    hasFieldError,
    validateFields,
    isValidDate,
    isValidEmail,
    isValidPhone,
    isValidPassportNumber,
    disabledDate,
    validateVisaInfo,
    handleDateChange,
    handleExpeditedChange,
    handleVisitedChange,
    handleVietnamContactChange,
    initializeValidation,
  }
}
