# 🚀 生产环境启动脚本
# 用于客户试用、frp隧道和生产部署
# 支持Windows PowerShell和跨平台PowerShell Core (pwsh)

Write-Host "🚀 启动Visa Automator生产环境..." -ForegroundColor Green

# 检查Docker是否运行（跨平台）
Write-Host "🔍 检查Docker服务状态..." -ForegroundColor Yellow
try {
    # 检查Docker daemon是否运行
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker服务运行正常" -ForegroundColor Green

        # 显示Docker版本信息
        $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
        if ($dockerVersion) {
            Write-Host "📦 Docker版本: $dockerVersion" -ForegroundColor Cyan
        }
    }
    else {
        throw "Docker daemon not responding"
    }
}
catch {
    Write-Host "❌ Docker未运行或未安装，请先启动Docker服务" -ForegroundColor Red
    Write-Host "💡 Windows: 启动Docker Desktop" -ForegroundColor Yellow
    Write-Host "💡 Linux: sudo systemctl start docker" -ForegroundColor Yellow
    Write-Host "💡 macOS: 启动Docker Desktop" -ForegroundColor Yellow
    exit 1
}

# 停止开发环境（如果在运行）
Write-Host "🛑 停止开发环境..." -ForegroundColor Yellow
docker compose stop

# 启动生产环境（不使用override）
Write-Host "📦 启动生产环境..." -ForegroundColor Yellow
docker compose -f docker-compose.yml -f docker-compose.production.yml up -d --build

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
docker compose -f docker-compose.yml -f docker-compose.production.yml ps

# 检查健康状态（带重试机制）
Write-Host "🏥 检查服务健康状态..." -ForegroundColor Yellow
$healthUrl = if ($env:HEALTH_CHECK_URL) { $env:HEALTH_CHECK_URL } else { "http://localhost:8000/health" }
Write-Host "🔍 健康检查URL: $healthUrl" -ForegroundColor Cyan

$maxRetries = 3
$retryDelay = 5  # 秒

for ($attempt = 1; $attempt -le $maxRetries; $attempt++) {
    try {
        if ($attempt -gt 1) {
            Write-Host "🔄 重试健康检查 ($attempt/$maxRetries)..." -ForegroundColor Yellow
        }

        $healthResponse = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 10 -ErrorAction Stop

        if ($healthResponse.StatusCode -eq 200) {
            Write-Host "✅ 服务健康检查通过 (状态码: $($healthResponse.StatusCode))" -ForegroundColor Green

            # 尝试解析响应内容以获取更多信息
            try {
                $healthData = $healthResponse.Content | ConvertFrom-Json
                if ($healthData.status) {
                    Write-Host "📊 服务状态: $($healthData.status)" -ForegroundColor Cyan
                }
            }
            catch {
                # 忽略JSON解析错误，只要HTTP状态码正确即可
            }
            break  # 成功，退出重试循环
        }
        else {
            Write-Host "⚠️ 健康检查返回状态码: $($healthResponse.StatusCode)" -ForegroundColor Yellow
            if ($attempt -eq $maxRetries) {
                Write-Host "❌ 服务健康检查失败，状态码: $($healthResponse.StatusCode)" -ForegroundColor Red
                Write-Host "💡 建议运行: docker compose logs -f" -ForegroundColor Yellow
                exit 1
            }
        }
    }
    catch {
        Write-Host "⚠️ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Yellow

        if ($attempt -eq $maxRetries) {
            Write-Host "❌ 服务健康检查最终失败" -ForegroundColor Red
            Write-Host "💡 可能原因:" -ForegroundColor Yellow
            Write-Host "   - 服务启动时间较长（正常情况下需要1-2分钟）" -ForegroundColor Yellow
            Write-Host "   - 网络连接问题" -ForegroundColor Yellow
            Write-Host "   - 服务配置错误" -ForegroundColor Yellow
            Write-Host "💡 建议运行: docker compose logs -f" -ForegroundColor Yellow
            exit 1
        }
    }

    if ($attempt -lt $maxRetries) {
        Write-Host "⏳ 等待 $retryDelay 秒后重试..." -ForegroundColor Cyan
        Start-Sleep -Seconds $retryDelay
    }
}

Write-Host "✅ 生产环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  应用入口:       http://localhost:8000" -ForegroundColor White
Write-Host "  登录页面:       http://localhost:8000/login" -ForegroundColor White
Write-Host "  API文档:        http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "🔗 frp隧道配置：" -ForegroundColor Cyan
Write-Host "  本地端口: 8000" -ForegroundColor White
Write-Host "  远程端口: 8000" -ForegroundColor White
Write-Host ""
Write-Host "📝 停止服务: docker compose -f docker-compose.yml -f docker-compose.production.yml down" -ForegroundColor Yellow
