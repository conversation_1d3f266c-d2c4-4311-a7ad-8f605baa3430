{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:unit:ui": "vitest --ui", "test:unit:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:check": "eslint .", "lint:fix": "eslint . --fix", "style:check": "stylelint \"src/**/*.{css,scss,vue}\"", "style:fix": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "format:check": "prettier --check src/", "format:fix": "prettier --write src/", "check": "run-p lint:check style:check format:check type-check", "fix": "run-s lint:fix style:fix format:fix", "clean": "rimraf dist node_modules/.cache .vite"}, "dependencies": {"@date-fns/tz": "^1.2.0", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "visa-automator": "file:..", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@pinia/testing": "^0.1.7", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.5.2", "@testing-library/vue": "^8.1.0", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^3.1.1", "@vitest/eslint-plugin": "^1.1.39", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^8.0.0", "prettier": "3.5.3", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.0", "stylelint": "^16.21.0", "stylelint-config-standard-vue": "^1.0.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}