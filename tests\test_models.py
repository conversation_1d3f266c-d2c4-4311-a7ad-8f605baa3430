"""
数据模型单元测试
=============

测试所有数据模型的验证逻辑、约束和关系
"""

from datetime import date

import pytest
from sqlalchemy.exc import IntegrityError

from app.data.models.application import Application
from app.data.models.automation_logs import AutomationLogs
from app.data.models.order import Order
from app.data.models.user import User
from tests.test_utils import ModernizedTestDataGenerator, TestDataFactory


class TestApplicantModel:
    """申请人模型测试"""

    @pytest.mark.asyncio
    async def test_create_applicant_basic(self, db_session, sample_user_data):
        """测试创建基本申请人 - 使用现代化数据生成器"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 创建申请人 - 使用现代化数据生成器，直接返回模型实例
        applicant = ModernizedTestDataGenerator.generate_applicant_data(user_id=user.id)
        db_session.add(applicant)
        await db_session.commit()

        assert applicant.user_id == user.id
        assert applicant.given_name is not None
        assert applicant.sex in ["M", "F", "MALE", "FEMALE"]
        assert applicant.passport_number is not None

    @pytest.mark.asyncio
    async def test_applicant_sex_validation(self, db_session, sample_user_data):
        """测试申请人性别验证"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 测试有效性别
        for sex in ["M", "F"]:
            applicant = TestDataFactory.create_applicant_data(
                {"user_id": user.id, "sex": sex}
            )
            # 直接使用返回的实例，不再解包
            db_session.add(applicant)
            await (
                db_session.flush()
            )  # 使用flush而不是commit，这样可以在同一事务中测试多个对象
            assert applicant.sex == sex

        # 测试无效性别
        with pytest.raises(ValueError, match="Invalid sex value"):
            applicant = TestDataFactory.create_applicant_data(
                {
                    "user_id": user.id,
                    "sex": "X",  # 无效性别
                }
            )

    @pytest.mark.asyncio
    async def test_applicant_passport_dates_constraint(
        self, db_session, sample_user_data
    ):
        """测试护照日期约束"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 测试无效日期（过期日期早于签发日期）
        applicant = TestDataFactory.create_applicant_data(
            {
                "user_id": user.id,
                "date_of_issue": date(2023, 1, 1),
                "date_of_expiry": date(2022, 1, 1),  # 过期日期早于签发日期
            }
        )
        db_session.add(applicant)

        # 这应该在数据库层面失败
        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_applicant_unique_constraint(self, db_session, sample_user_data):
        """测试申请人唯一约束"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        passport_number = "TEST123456"

        # 创建第一个申请人
        applicant1 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": passport_number}
        )
        db_session.add(applicant1)
        await db_session.commit()

        # 尝试创建相同护照号的申请人（应该失败）
        applicant2 = TestDataFactory.create_applicant_data(
            {"user_id": user.id, "passport_number": passport_number}
        )
        db_session.add(applicant2)

        with pytest.raises(IntegrityError):
            await db_session.commit()


class TestApplicationModel:
    """申请模型测试"""

    @pytest.mark.asyncio
    async def test_create_application_basic(self, db_session, sample_user_data):
        """测试创建基本申请"""
        # 先创建用户和申请人
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        # 创建申请
        application = TestDataFactory.create_application_data(
            {"applicant_id": applicant.id, "user_id": user.id}
        )
        db_session.add(application)
        await db_session.commit()

        assert application.country == "VNM"
        assert application.category == "tourist"
        assert application.visa_entry_type == "Single-entry"

    @pytest.mark.asyncio
    async def test_application_country_validation(self, db_session, sample_user_data):
        """测试国家代码验证"""
        # 先创建用户和申请人
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        # 测试有效的国家代码
        valid_countries = ["VNM", "CHN", "USA", "GBR"]
        for country in valid_countries:
            application = TestDataFactory.create_application_data(
                {"applicant_id": applicant.id, "user_id": user.id, "country": country}
            )
            assert application.validate_country("country", country) == country

        # 测试无效的国家代码
        with pytest.raises(ValueError, match="Invalid country code"):
            application = Application()
            application.validate_country("country", "INVALID")

    @pytest.mark.asyncio
    async def test_application_category_validation(self, db_session):
        """测试签证类别验证"""
        valid_categories = ["tourist", "business", "working", "visit_family", "transit"]

        for category in valid_categories:
            application = Application()
            result = application.validate_category("category", category)
            assert result == category

        # 测试无效类别
        with pytest.raises(ValueError, match="Invalid visa category"):
            application = Application()
            application.validate_category("category", "invalid_category")

    @pytest.mark.asyncio
    async def test_application_visa_entry_type_validation(self, db_session):
        """测试签证入境类型验证"""
        valid_types = ["Single-entry", "Multiple-entry"]

        for entry_type in valid_types:
            application = Application()
            result = application.validate_visa_entry_type("visa_entry_type", entry_type)
            assert result == entry_type

        # 测试无效类型
        with pytest.raises(ValueError, match="Invalid visa entry type"):
            application = Application()
            application.validate_visa_entry_type("visa_entry_type", "invalid_type")


class TestOrderModel:
    """订单模型测试"""

    @pytest.mark.asyncio
    async def test_create_order_basic(self, db_session, sample_user_data):
        """测试创建基本订单 - 使用现代化数据生成器"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 创建订单 - 使用现代化数据生成器，直接返回模型实例
        order = ModernizedTestDataGenerator.generate_order_data(user_id=user.id)

        assert order.order_status in [
            "created",
            "paid",
            "processing",
            "completed",
            "cancelled",
        ]
        assert order.order_type == "visa_application"
        assert order.user_id == user.id
        assert order.order_no.startswith("VN")

    @pytest.mark.asyncio
    async def test_order_unique_order_no(self, db_session, sample_user_data):
        """测试订单号唯一约束"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        order_no = "VN20250621TEST001"

        # 创建第一个订单
        order1 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": order_no}
        )
        db_session.add(order1)
        await db_session.commit()

        # 尝试创建相同订单号的订单（应该失败）
        order2 = TestDataFactory.create_order_data(
            {"user_id": user.id, "order_no": order_no}
        )
        db_session.add(order2)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_default_values(self, db_session, sample_user_data):
        """测试订单默认值"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 创建最小化订单（测试默认值）
        order = Order(user_id=user.id, order_no="VN20250621TEST002")

        db_session.add(order)
        await db_session.commit()

        assert order.order_status == "created"  # 默认状态
        assert order.order_type == "visa_application"  # 默认类型
        assert order.created_at is not None
        assert order.updated_at is not None


class TestUserModel:
    """用户模型测试"""

    @pytest.mark.asyncio
    async def test_create_user_basic(self, db_session):
        """测试创建基本用户"""
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()

        assert user.username is not None
        assert user.email is not None
        assert user.is_active is True
        assert user.is_verified is True
        assert user.role == "user"

    @pytest.mark.asyncio
    async def test_user_unique_constraints(self, db_session):
        """测试用户唯一约束"""
        user1 = TestDataFactory.create_user_data()
        db_session.add(user1)
        await db_session.commit()

        # 尝试创建相同用户名的用户
        user2 = TestDataFactory.create_user_data()
        user2.username = user1.username  # 设置相同的用户名
        user2.email = "<EMAIL>"  # 不同的邮箱
        db_session.add(user2)

        with pytest.raises(IntegrityError):
            await db_session.commit()


class TestVisaPaymentModel:
    """签证支付模型测试"""

    @pytest.mark.asyncio
    async def test_create_visa_payment_basic(self, db_session, sample_user_data):
        """测试创建基本签证支付"""
        # 先创建用户、申请人和申请
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        application = TestDataFactory.create_application_data(
            {"applicant_id": applicant.id, "user_id": user.id}
        )
        db_session.add(application)
        await db_session.commit()

        order = TestDataFactory.create_order_data({"user_id": user.id})
        db_session.add(order)
        await db_session.commit()

        payment = TestDataFactory.create_visa_payment_data(
            {"user_id": user.id, "order_id": order.id, "application_id": application.id}
        )
        db_session.add(payment)
        await db_session.commit()

        assert payment.user_id == user.id
        assert payment.application_id == application.id
        assert payment.status == "created"


class TestFileModel:
    """文件模型测试"""

    @pytest.mark.asyncio
    async def test_create_file_basic(self, db_session, sample_user_data):
        """测试创建基本文件记录"""
        # 先创建用户、申请人和申请
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        application = TestDataFactory.create_application_data(
            {"applicant_id": applicant.id, "user_id": user.id}
        )
        db_session.add(application)
        await db_session.commit()

        # 创建文件记录 - 使用正确的字段
        file_record = TestDataFactory.create_file_data(
            {"application_id": application.id}
        )
        db_session.add(file_record)
        await db_session.commit()

        assert file_record.application_id == application.id
        assert file_record.file_type == "passport"


class TestAutomationLogsModel:
    """自动化日志模型测试"""

    @pytest.mark.asyncio
    async def test_create_automation_log_basic(self, db_session, sample_user_data):
        """测试创建基本自动化日志"""
        # 先创建用户、申请人和申请
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        application = TestDataFactory.create_application_data(
            {"applicant_id": applicant.id, "user_id": user.id}
        )
        db_session.add(application)
        await db_session.commit()

        order = TestDataFactory.create_order_data({"user_id": user.id})
        db_session.add(order)
        await db_session.commit()

        # 创建自动化日志 - 使用正确的字段
        log = AutomationLogs(
            application_id=application.id,
            order_id=order.id,
            task_type="vietnam_evisa",
            task_status="processing",
        )
        db_session.add(log)
        await db_session.commit()

        assert log.application_id == application.id
        assert log.order_id == order.id
        assert log.task_type == "vietnam_evisa"
        assert log.task_status == "processing"


class TestModelRelationships:
    """模型关系测试"""

    @pytest.mark.asyncio
    async def test_user_applicant_relationship(self, db_session, sample_user_data):
        """测试用户-申请人关系"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 创建申请人
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        # 验证关系
        assert applicant.user_id == user.id

    @pytest.mark.asyncio
    async def test_cascade_delete(self, db_session, sample_user_data):
        """测试级联删除"""
        # 先创建用户
        user = User(**sample_user_data)
        db_session.add(user)
        await db_session.commit()

        # 创建申请人和订单
        applicant = TestDataFactory.create_applicant_data({"user_id": user.id})
        db_session.add(applicant)
        await db_session.commit()

        order = TestDataFactory.create_order_data({"user_id": user.id})
        db_session.add(order)
        await db_session.commit()

        # 删除用户
        await db_session.delete(user)
        await db_session.commit()

        # 验证相关记录也被删除（根据外键约束设置）
        # 这里的具体行为取决于你的外键约束设置
