# 多阶段构建：第一阶段 - 构建前端
FROM node:20-alpine AS build-stage

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装所有依赖（构建需要开发依赖）
RUN npm install

# 复制源代码
COPY . .

# 构建生产版本（无 Source Maps）
ARG VITE_API_BASE_URL
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV NODE_ENV=production

RUN npm run build

# 第二阶段 - 生产运行环境
FROM nginx:alpine

# 复制构建产物
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
