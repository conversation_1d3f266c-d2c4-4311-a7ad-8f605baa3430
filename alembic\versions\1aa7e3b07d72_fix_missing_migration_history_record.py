"""fix_missing_migration_history_record

Revision ID: 1aa7e3b07d72
Revises: ff1a99ba5860
Create Date: 2025-07-09 18:38:23.333439

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1aa7e3b07d72'
down_revision: Union[str, None] = 'ff1a99ba5860'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 只做一件事：补充遗漏的migration_history记录
    op.execute("""
        INSERT INTO migration_history (version_num, migration_name, description, tables_affected, success)
        VALUES ('ff1a99ba5860', 'refactor_application_number_field_correct',
                '删除vietnam_application_number字段，添加通用application_number字段(String 64, unique=True)，同时在visa_status_history表添加application_number字段增强数据一致性',
                'application,visa_status_history', true)
    """)


def downgrade() -> None:
    # 删除这条记录
    op.execute("DELETE FROM migration_history WHERE version_num = 'ff1a99ba5860'")
