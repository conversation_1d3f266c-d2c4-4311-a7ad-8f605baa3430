"""refactor_application_number_field_correct

Revision ID: ff1a99ba5860
Revises: 65d946459ccb
Create Date: 2025-07-09 18:12:23.333439

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ff1a99ba5860'
down_revision: Union[str, None] = '65d946459ccb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### 只保留application_number相关的最小变更 ###

    # 1. 添加新的通用application_number字段到application表
    op.add_column('application', sa.Column('application_number', sa.String(length=64), nullable=True, doc="申请编号（通用字段）"))

    # 2. 为新字段创建唯一索引（与模型定义保持一致）
    op.create_index('ix_app_application_number', 'application', ['application_number'], unique=True)

    # 3. 删除旧的vietnam_application_number字段（数据为空，无需迁移）
    op.drop_column('application', 'vietnam_application_number')

    # 4. 添加application_number字段到visa_status_history表（增强数据一致性）
    op.add_column('visa_status_history', sa.Column('application_number', sa.String(length=64), nullable=True, doc="申请编号快照（增强数据一致性）"))


def downgrade() -> None:
    # ### 回滚变更 ###

    # 1. 从visa_status_history表删除application_number字段
    op.drop_column('visa_status_history', 'application_number')

    # 2. 重新添加vietnam_application_number字段
    op.add_column('application', sa.Column('vietnam_application_number', sa.String(length=64), nullable=True, doc="越南申请编号"))

    # 3. 删除唯一索引
    op.drop_index('ix_app_application_number', table_name='application')

    # 4. 删除通用application_number字段
    op.drop_column('application', 'application_number')
