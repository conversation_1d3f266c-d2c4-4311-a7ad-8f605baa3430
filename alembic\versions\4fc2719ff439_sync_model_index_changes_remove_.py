"""sync model index changes - remove redundant index definitions

Revision ID: 4fc2719ff439
Revises: b73353f1b380
Create Date: 2025-07-06 08:41:00.404027

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4fc2719ff439'
down_revision: Union[str, None] = 'b73353f1b380'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    🔧 模型索引定义同步 - 消除重复索引定义

    本次迁移的目的：
    ================

    1. 📋 问题背景：
       - 模型文件中的ForeignKey字段设置了index=True
       - 同时migration文件中也手动创建了相同的索引
       - 这导致了重复的索引定义和潜在的冲突

    2. 🔧 解决方案：
       - 移除了模型文件中ForeignKey字段的重复index=True设置
       - 保留migration中已创建的索引（作为索引的权威定义源）
       - 保留模型中独有的业务复合索引

    3. 📊 具体变更：

       移除的模型索引定义（index=True）：
       ================================
       - automation_logs: application_id, order_id, task_type, task_status
       - application: user_id, order_id, applicant_id, vietnam_application_number
       - applicant: user_id
       - order: user_id
       - visa_status_history: user_id, application_id, order_id
       - visa_payment: user_id, order_id, application_id
       - user_payment: user_id, order_id
       - file: application_id

       移除的重复复合索引：
       ==================
       - automation_logs: 所有复合索引（已在create_automation_logs_table.py中创建）
       - order: ix_order_user_status（已在add_missing_order_fields.py中创建）
       - visa_status_history: 用户和订单相关复合索引（已在相应migration中创建）
       - visa_payment: 所有复合索引（已在add_user_id_order_id_to_visa_payment.py中创建）
       - application: 用户相关复合索引（已在add_user_id_fields.py中创建）

    4. ✅ 结果：
       - 消除了23个重复索引定义
       - 解决了automation_logs表的索引冲突问题
       - 统一了索引管理：migration作为权威定义源
       - 保持了数据库性能：所有必要的索引都存在

    注意：此migration不执行任何数据库操作，仅用于同步模型定义与数据库状态。
    所有索引都通过之前的migration正确创建，数据库结构保持不变。
    """
    # 📊 更新migration_history表记录此次迁移
    op.execute("""
        INSERT INTO migration_history (version_num, migration_name, description, tables_affected, success)
        VALUES (
            '4fc2719ff439',
            'sync_model_index_changes_remove_redundant_index_definitions',
            'sync model index changes - remove redundant index definitions from model files',
            'automation_logs,application,applicant,order,visa_status_history,visa_payment,user_payment,file',
            true
        )
    """)

    # 无需执行任何数据库结构操作
    # 所有索引已通过之前的migration正确创建
    pass


def downgrade() -> None:
    """
    回滚说明：
    =========

    本migration的回滚不需要任何数据库操作，因为：
    1. 数据库索引结构未发生变化
    2. 只是模型定义的同步
    3. 要回滚此变更，需要在模型文件中重新添加index=True设置
    """
    # 📊 从migration_history表中移除此次迁移记录
    op.execute("""
        DELETE FROM migration_history
        WHERE version_num = '4fc2719ff439'
    """)

    # 无需执行任何数据库结构操作
    pass
