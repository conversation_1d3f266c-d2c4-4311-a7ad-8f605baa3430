/**
 * 性能监控工具
 * =============
 *
 * 监控架构改进后的性能指标和稳定性
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  category: 'store' | 'api' | 'component' | 'polling'
}

interface StoreMetrics {
  storeCount: number
  stateSize: number
  updateFrequency: number
  memoryUsage: number
}

interface PollingMetrics {
  activePolls: number
  pollInterval: number
  successRate: number
  averageResponseTime: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private observers: Map<string, PerformanceObserver> = new Map()
  private startTime: number = Date.now()

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    // 监控导航性能
    if ('PerformanceObserver' in window) {
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('navigation', entry.duration, 'component')
        }
      })
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.set('navigation', navObserver)

      // 监控资源加载
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('resource_load', entry.duration, 'component')
        }
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.set('resource', resourceObserver)
    }
  }

  /**
   * 记录性能指标
   */
  recordMetric(name: string, value: number, category: PerformanceMetric['category']) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      category,
    }

    this.metrics.push(metric)

    // 保持最近1000条记录
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }

    // 🔧 修复：移除调试日志
  }

  /**
   * 监控Store性能
   */
  monitorStorePerformance(): StoreMetrics {
    const stateSize = this.estimateStateSize()

    return {
      storeCount: this.getActiveStoreCount(),
      stateSize,
      updateFrequency: this.calculateUpdateFrequency(),
      memoryUsage: this.estimateMemoryUsage(),
    }
  }

  /**
   * 监控轮询性能
   */
  monitorPollingPerformance(): PollingMetrics {
    const pollingMetrics = this.metrics.filter((m) => m.category === 'polling')
    const recentPolling = pollingMetrics.slice(-10)

    return {
      activePolls: this.getActivePollingCount(),
      pollInterval: this.getAveragePollingInterval(),
      successRate: this.calculatePollingSuccessRate(recentPolling),
      averageResponseTime: this.calculateAverageResponseTime(recentPolling),
    }
  }

  /**
   * 监控API性能
   */
  monitorApiPerformance() {
    const apiMetrics = this.metrics.filter((m) => m.category === 'api')
    const recent = apiMetrics.slice(-20)

    return {
      totalRequests: apiMetrics.length,
      averageResponseTime: recent.reduce((sum, m) => sum + m.value, 0) / recent.length || 0,
      errorRate: this.calculateApiErrorRate(recent),
      slowRequests: recent.filter((m) => m.value > 3000).length,
    }
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const now = Date.now()
    const uptime = now - this.startTime

    const report = {
      timestamp: now,
      uptime,
      store: this.monitorStorePerformance(),
      polling: this.monitorPollingPerformance(),
      api: this.monitorApiPerformance(),
      memory: this.getMemoryInfo(),
      recommendations: this.generateRecommendations(),
    }

    // 🔧 修复：移除调试日志
    return report
  }

  /**
   * 开始监控API调用
   */
  startApiMonitoring() {
    const originalFetch = window.fetch

    window.fetch = async (...args) => {
      const startTime = performance.now()

      try {
        const response = await originalFetch(...args)
        const endTime = performance.now()

        this.recordMetric('api_request', endTime - startTime, 'api')

        return response
      } catch (error) {
        const endTime = performance.now()
        this.recordMetric('api_error', endTime - startTime, 'api')
        throw error
      }
    }
  }

  /**
   * 监控Store状态变化
   */
  monitorStoreChanges(storeName: string, callback: () => void) {
    const startTime = performance.now()

    return (...args: Parameters<typeof callback>) => {
      const result = callback.apply(this, args)
      const endTime = performance.now()

      this.recordMetric(`store_${storeName}_update`, endTime - startTime, 'store')

      return result
    }
  }

  /**
   * 监控组件渲染性能
   */
  monitorComponentRender(componentName: string) {
    return {
      onBeforeMount: () => {
        performance.mark(`${componentName}_mount_start`)
      },
      onMounted: () => {
        performance.mark(`${componentName}_mount_end`)
        performance.measure(
          `${componentName}_mount`,
          `${componentName}_mount_start`,
          `${componentName}_mount_end`,
        )

        const measure = performance.getEntriesByName(`${componentName}_mount`)[0]
        if (measure) {
          this.recordMetric(`component_${componentName}_mount`, measure.duration, 'component')
        }
      },
    }
  }

  // 私有辅助方法
  private getActiveStoreCount(): number {
    // 估算活跃的store数量
    return 5 // 基于我们的架构改进，从6个减少到5个
  }

  private estimateStateSize(): number {
    // 估算状态大小（KB）
    const stateStr = JSON.stringify(localStorage)
    return new Blob([stateStr]).size / 1024
  }

  private calculateUpdateFrequency(): number {
    const storeMetrics = this.metrics.filter((m) => m.category === 'store')
    const recentUpdates = storeMetrics.filter((m) => Date.now() - m.timestamp < 60000)
    return recentUpdates.length
  }

  private estimateMemoryUsage(): number {
    // 估算内存使用（MB）
    if ('memory' in performance) {
      const perfWithMemory = performance as Performance & {
        memory?: { usedJSHeapSize: number }
      }
      return perfWithMemory.memory?.usedJSHeapSize
        ? perfWithMemory.memory.usedJSHeapSize / 1024 / 1024
        : 0
    }
    return 0
  }

  private getActivePollingCount(): number {
    // 基于我们的改进，统一轮询应该只有1个活跃轮询
    return 1
  }

  private getAveragePollingInterval(): number {
    // 基于useTaskPolling的配置
    return 5000 // 5秒
  }

  private calculatePollingSuccessRate(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 100
    const successful = metrics.filter((m) => !m.name.includes('error')).length
    return (successful / metrics.length) * 100
  }

  private calculateAverageResponseTime(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 0
    return metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length
  }

  private calculateApiErrorRate(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 0
    const errors = metrics.filter((m) => m.name.includes('error')).length
    return (errors / metrics.length) * 100
  }

  private getMemoryInfo() {
    if ('memory' in performance) {
      const perfWithMemory = performance as Performance & {
        memory?: {
          usedJSHeapSize: number
          totalJSHeapSize: number
          jsHeapSizeLimit: number
        }
      }

      if (perfWithMemory.memory) {
        return {
          used: Math.round(perfWithMemory.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(perfWithMemory.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(perfWithMemory.memory.jsHeapSizeLimit / 1024 / 1024),
        }
      }
    }
    return null
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    const storeMetrics = this.monitorStorePerformance()
    const pollingMetrics = this.monitorPollingPerformance()

    if (storeMetrics.updateFrequency > 100) {
      recommendations.push('Store更新频率过高，考虑批量更新')
    }

    if (pollingMetrics.averageResponseTime > 2000) {
      recommendations.push('轮询响应时间过长，检查网络或服务器性能')
    }

    if (storeMetrics.memoryUsage > 100) {
      recommendations.push('内存使用过高，检查是否有内存泄漏')
    }

    return recommendations
  }

  /**
   * 清理监控器
   */
  cleanup() {
    this.observers.forEach((observer) => observer.disconnect())
    this.observers.clear()
    this.metrics = []
  }
}

// 创建全局监控实例
export const performanceMonitor = new PerformanceMonitor()

// 自动开始监控
performanceMonitor.startApiMonitoring()

// 定期生成报告
setInterval(() => {
  performanceMonitor.generateReport()
}, 60000) // 每分钟生成一次报告
