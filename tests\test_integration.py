"""
端到端集成测试
测试完整的签证申请流程、数据一致性和系统集成
"""

from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch
import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.visa_automation_engine import VisaAutomationEngine
from app.data.model import VietnamEVisaApplicant
from app.data.models.applicant import Applicant
from app.data.models.automation_logs import AutomationLogs
from app.data.models.order import Order
from app.data.models.user import User
from app.fillers.vietnam_filler import VietnamFiller
from app.services.order_service import OrderService


class TestVisaAutomationIntegration:
    """真正的集成测试 - 测试组件间的真实交互"""

    @pytest.fixture
    def real_engine(self):
        """使用真实配置的自动化引擎"""
        return VisaAutomationEngine()

    @pytest.fixture
    def real_filler(self):
        """使用真实配置的表单填写器"""
        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)
        return filler

    @pytest.fixture
    def sample_applicant(self):
        """示例申请人数据"""
        return VietnamEVisaApplicant(
            passport_number="E12345678",
            chinese_name="张伟",
            surname="<PERSON>",
            given_name="Wei",
            dob="01/01/1990",
            nationality="CHINA",
            sex="MALE",
            email="<EMAIL>",
            telephone_number="13800138000",
            permanent_address="Beijing, China",
            contact_address="Beijing, China",
            visa_entry_type="single",
            visa_validity_duration="30天",
            visa_start_date="01/07/2025",
            intended_entry_gate="NHAT_TAN_BRIDGE",
            purpose_of_entry="Tourist",
            visited_vietnam_last_year=False,
            has_vietnam_contact=False,
        )

    def test_engine_filler_integration(
        self, real_engine, real_filler, sample_applicant
    ):
        """测试引擎和填写器的集成"""
        # 测试配置共享
        assert real_engine.settings == real_filler.settings
        assert real_engine.locators["vietnam_evisa"] == real_filler.locators

        # 测试URL配置一致性
        assert real_engine.settings["vietnam_evisa_url"] == real_filler.base_url

    def test_filler_business_logic_integration(self, real_filler, sample_applicant):
        """测试填写器内部业务逻辑集成"""
        # 测试地址处理逻辑
        real_filler._ensure_addresses(sample_applicant)

        # 测试日期计算逻辑
        start_date = "01/01/2025"
        end_date = real_filler._calculate_end_date(start_date, "30天")
        assert end_date == "30/01/2025"

        # 测试定位器解析逻辑
        locator = real_filler.get_locator("homepage.apply_now_button")
        if locator:
            assert isinstance(locator, str)

    def test_configuration_consistency(self, real_engine, real_filler):
        """测试配置一致性"""
        # 测试所有组件使用相同的配置
        assert real_engine.settings["vietnam_evisa_url"] != ""
        assert real_filler.base_url != ""
        assert real_engine.settings["vietnam_evisa_url"] == real_filler.base_url

        # 测试配置类型正确性
        assert isinstance(real_engine.settings["headless"], bool)
        assert isinstance(real_engine.settings["slow_mo"], int)

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_automation_workflow_integration(
        self, mock_playwright, real_engine, sample_applicant
    ):
        """测试自动化工作流集成 - 只Mock外部依赖"""
        # 只Mock Playwright（外部依赖）
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # Mock浏览器对象
        mock_browser = Mock()
        mock_context = Mock()
        mock_page = Mock()
        mock_page.goto.side_effect = Exception("Test exception")

        with patch(
            "app.core.visa_automation_engine.launch_form_browser"
        ) as mock_launch:
            mock_launch.return_value = (mock_browser, mock_context, mock_page)

            # 测试真实的业务流程
            result = real_engine.run_vietnam_evisa_step1(sample_applicant)

            # 验证结果
            assert result is False

            # 验证真实的重试逻辑
            assert mock_launch.call_count == 3  # 应该重试3次


class TestDataFlowIntegration:
    """数据流集成测试 - 只Mock数据库连接"""

    @pytest.fixture
    async def mock_session(self):
        """Mock数据库会话 - 这是外部依赖"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "id": uuid.uuid4(),
            "email": "<EMAIL>",
            "username": "testuser",
            "is_active": True,
            "created_at": datetime.now(),
        }

    @pytest.fixture
    def sample_applicant_data(self):
        """示例申请人数据"""
        return {
            "surname": "Zhang",
            "given_name": "Wei",
            "chinese_name": "张伟",
            "sex": "MALE",
            "dob": "01/01/1990",
            "nationality": "CHINA",
            "passport_number": "E12345678",
            "passport_type": "Ordinary passport",
            "email": "<EMAIL>",
            "telephone_number": "13800138000",
            "permanent_address": "Beijing, China",
            "contact_address": "Beijing, China",
            "visa_entry_type": "single",
            "visa_validity_duration": "30天",
            "visa_start_date": "01/07/2025",
            "intended_entry_gate": "NHAT_TAN_BRIDGE",
            "purpose_of_entry": "Tourist",
            "visited_vietnam_last_year": False,
            "has_vietnam_contact": False,
        }

    @pytest.fixture
    def sample_order_data(self):
        """示例订单数据"""
        return {
            "passport_number": "E12345678",
            "applicant_name": "张伟",
            "visa_category": "tourist",
            "processing_type": "standard",
            "expedited_type": None,
            "order_type": "visa_application",
        }

    @pytest.mark.asyncio
    async def test_data_model_relationships(
        self, mock_session, sample_user_data, sample_applicant_data, sample_order_data
    ):
        """测试数据模型关系"""
        # 测试真实的数据模型创建和关系

        # 创建用户
        user = User(
            id=sample_user_data["id"],
            email=sample_user_data["email"],
            username=sample_user_data["username"],
            is_active=sample_user_data["is_active"],
            created_at=sample_user_data["created_at"],
        )

        # 创建申请人
        applicant = Applicant(
            id=uuid.uuid4(),
            passport_number=sample_applicant_data["passport_number"],
            chinese_name=sample_applicant_data["chinese_name"],
            surname=sample_applicant_data["surname"],
            given_name=sample_applicant_data["given_name"],
            sex=sample_applicant_data["sex"],
            date_of_birth=sample_applicant_data["dob"],
            nationality=sample_applicant_data["nationality"],
            user_id=user.id,
        )

        # 创建订单
        order = Order(
            id=uuid.uuid4(),
            order_no="VN202407050001",
            order_type=sample_order_data["order_type"],
            user_id=user.id,
            order_status="created",
        )

        # 验证数据模型关系
        assert user.id == applicant.user_id
        assert user.id == order.user_id
        assert applicant.passport_number == sample_applicant_data["passport_number"]
        assert order.order_type == sample_order_data["order_type"]

    @pytest.mark.asyncio
    async def test_business_logic_data_flow(
        self, mock_session, sample_user_data, sample_applicant_data
    ):
        """测试业务逻辑数据流"""
        # 测试数据转换逻辑
        applicant_data = sample_applicant_data.copy()

        # 测试真实的数据验证逻辑
        vietnam_applicant = VietnamEVisaApplicant(
            passport_number=applicant_data["passport_number"],
            chinese_name=applicant_data["chinese_name"],
            surname=applicant_data["surname"],
            given_name=applicant_data["given_name"],
            dob=applicant_data["dob"],
            nationality=applicant_data["nationality"],
            sex=applicant_data["sex"],
            email=applicant_data["email"],
            telephone_number=applicant_data["telephone_number"],
            permanent_address=applicant_data["permanent_address"],
            contact_address=applicant_data["contact_address"],
            visa_entry_type=applicant_data["visa_entry_type"],
            visa_validity_duration=applicant_data["visa_validity_duration"],
            visa_start_date=applicant_data["visa_start_date"],
            intended_entry_gate=applicant_data["intended_entry_gate"],
            purpose_of_entry=applicant_data["purpose_of_entry"],
            visited_vietnam_last_year=applicant_data["visited_vietnam_last_year"],
            has_vietnam_contact=applicant_data["has_vietnam_contact"],
        )

        # 验证数据转换正确性
        assert vietnam_applicant.passport_number == applicant_data["passport_number"]
        assert vietnam_applicant.chinese_name == applicant_data["chinese_name"]
        assert vietnam_applicant.nationality == applicant_data["nationality"]

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, mock_session):
        """测试错误处理集成"""
        # 测试真实的错误处理逻辑

        # 创建无效的申请人数据
        invalid_applicant = VietnamEVisaApplicant(
            passport_number="",  # 无效的护照号
            chinese_name="",
            surname="",
            given_name="",
            dob="",
            nationality="",
            sex="",
        )

        # 测试数据验证
        assert invalid_applicant.passport_number == ""
        assert invalid_applicant.chinese_name == ""

        # 测试错误记录
        error_log = AutomationLogs(
            order_id=uuid.uuid4(),
            celery_task_id="test-task",
            task_status="failed",
            task_type="vietnam_evisa",
            started_at=datetime.now(),
            completed_at=datetime.now(),
            error_message="数据验证失败",
        )

        # 验证错误记录结构
        assert error_log.task_status == "failed"
        assert error_log.error_message == "数据验证失败"


class TestServiceIntegration:
    """服务层集成测试"""

    @pytest.fixture
    async def mock_session(self):
        """Mock数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.mark.asyncio
    async def test_order_service_integration(self, mock_session):
        """测试订单服务集成"""
        from app.repositories.order_repository import OrderRepository
        from backend.models.order import OrderStatus

        # Mock repository（数据库层）
        mock_repo = Mock(spec=OrderRepository)
        mock_repo.update_order_status = AsyncMock(
            return_value={
                "success": True,
                "data": {
                    "order_no": "VN202407050001",
                    "old_status": "created",
                    "new_status": "cancelled",
                    "updated_at": datetime.now().isoformat(),
                },
                "message": "订单状态已更新",
            }
        )

        # 测试真实的服务逻辑
        order_service = OrderService(mock_repo)

        result = await order_service.update_order_status(
            user_id=uuid.uuid4(),
            order_no="VN202407050001",
            new_status=OrderStatus.CANCELLED,
            reason="测试取消",
            operator_type="system",
        )

        # 验证服务层逻辑
        assert result["success"] is True
        assert result["data"]["new_status"] == "cancelled"

        # 验证repository被正确调用
        mock_repo.update_order_status.assert_called_once()

    @pytest.mark.asyncio
    async def test_automation_service_integration(self, mock_session):
        """测试自动化服务集成"""
        # 测试真实的自动化服务逻辑

        # 创建自动化任务记录
        automation_log = AutomationLogs(
            order_id=uuid.uuid4(),
            celery_task_id="automation-task-123",
            task_status="running",
            task_type="vietnam_evisa",
            started_at=datetime.now(),
        )

        # 验证任务记录结构
        assert automation_log.task_status == "running"
        assert automation_log.task_type == "vietnam_evisa"
        assert automation_log.started_at is not None

    @pytest.mark.asyncio
    async def test_notification_service_integration(self, mock_session):
        """测试通知服务集成"""
        # 测试真实的通知逻辑

        # 创建通知数据
        notification_data = {
            "user_email": "<EMAIL>",
            "order_no": "VN202407050001",
            "status": "completed",
            "message": "签证申请已完成",
        }

        # 验证通知数据结构
        assert notification_data["user_email"] != ""
        assert notification_data["order_no"] != ""
        assert notification_data["status"] in ["completed", "failed", "processing"]


class TestSystemIntegration:
    """系统级集成测试"""

    def test_configuration_system_integration(self):
        """测试配置系统集成"""
        # 测试所有组件使用一致的配置
        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)

        # 验证配置一致性
        assert engine.settings["vietnam_evisa_url"] == filler.base_url

        # 验证配置完整性
        required_settings = [
            "vietnam_evisa_url",
            "browser",
            "headless",
            "slow_mo",
            "anti_captcha_api_key",
        ]

        for setting in required_settings:
            assert setting in engine.settings

    def test_locator_system_integration(self):
        """测试定位器系统集成"""
        # 测试定位器系统
        engine = VisaAutomationEngine()
        filler = VietnamFiller()
        filler.prepare(engine.locators, engine.settings)

        # 验证定位器结构
        if "vietnam_evisa" in engine.locators:
            assert engine.locators["vietnam_evisa"] == filler.locators

            # 测试定位器解析
            homepage_locator = filler.get_locator("homepage.apply_now_button")
            if homepage_locator:
                assert isinstance(homepage_locator, str)

    def test_data_validation_system_integration(self):
        """测试数据验证系统集成"""
        # 测试数据验证流程

        # 创建完整的申请人数据
        applicant = VietnamEVisaApplicant(
            passport_number="E12345678",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="CHINA",
            sex="MALE",
            email="<EMAIL>",
            telephone_number="13800138000",
            permanent_address="Beijing, China",
            contact_address="Beijing, China",
            visa_entry_type="single",
            visa_validity_duration="30天",
            visa_start_date="01/07/2025",
            intended_entry_gate="NHAT_TAN_BRIDGE",
            purpose_of_entry="Tourist",
            visited_vietnam_last_year=False,
            has_vietnam_contact=False,
        )

        # 验证数据完整性
        assert applicant.passport_number != ""
        assert applicant.chinese_name != ""
        assert applicant.surname != ""
        assert applicant.given_name != ""
        assert applicant.dob != ""
        assert applicant.nationality != ""
        assert applicant.sex != ""

        # 验证数据类型
        assert isinstance(applicant.visited_vietnam_last_year, bool)
        assert isinstance(applicant.has_vietnam_contact, bool)

    @patch("app.core.visa_automation_engine.sync_playwright")
    def test_end_to_end_workflow_integration(self, mock_playwright):
        """测试端到端工作流集成"""
        # 只Mock最外层的依赖
        mock_p_context = Mock()
        mock_playwright.return_value.start.return_value = mock_p_context

        # 测试真实的端到端流程
        engine = VisaAutomationEngine()
        applicant = VietnamEVisaApplicant(
            passport_number="E12345678",
            chinese_name="张伟",
            surname="Zhang",
            given_name="Wei",
            dob="01/01/1990",
            nationality="CHINA",
            sex="MALE",
        )

        # Mock浏览器启动失败来测试错误处理
        with patch(
            "app.core.visa_automation_engine.launch_form_browser"
        ) as mock_launch:
            mock_launch.side_effect = Exception("Browser launch failed")

            # 测试完整的错误处理流程
            result = engine.run_vietnam_evisa_step1(applicant)

            # 验证错误处理
            assert result is False

            # 验证重试机制
            assert mock_launch.call_count == 3
