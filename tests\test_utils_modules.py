"""
工具模块测试 (现代化版本)
====================

测试各种工具函数和实用程序，使用现代化测试模式：
- 地址生成工具
- 日期计算工具
- 验证码解析器
- 环境变量加载器
- 日志配置
- 安全工具

现代化特性：
- @pytest.mark.parametrize 参数化测试
- freezegun 时间冻结
- Faker 真实测试数据
- 更好的边界情况覆盖
"""

import datetime
import os
import tempfile
from unittest.mock import Mock, patch

from faker import Faker
from freezegun import freeze_time
import pytest

# 导入被测试的模块
from app.utils.address_tools import CHINA_PROVINCE_CITY_MAP, generate_random_address
from app.utils.captcha_solver import CaptchaSolver, solve_captcha
from app.utils.date_utils import (
    VIETNAM_HOLIDAYS_2025,
    calculate_working_days_from_today,
)
from app.utils.env_loader import load_credit_cards_from_env
from app.utils.logger_config import get_logger, setup_logger
from app.utils.security import mask_card_number, mask_email, mask_sensitive_data

fake = Faker()


class TestAddressTools:
    """现代化地址生成工具测试 - 使用参数化测试"""

    @pytest.mark.parametrize(
        "province, expected_suffix",
        [
            ("GUANGDONG", "Guangdong Province, China"),
            ("BEIJING", "Beijing, China"),
            (
                "XINJIANG UYGUR AUTONOMOUS REGION",
                "Xinjiang Uygur Autonomous Region, China",
            ),
        ],
    )
    def test_generate_random_address_valid_provinces(self, province, expected_suffix):
        """测试有效省份的地址生成 - 参数化测试覆盖多种情况"""
        address = generate_random_address(province)
        assert address.endswith(expected_suffix)
        assert "City," in address or province in [
            "BEIJING",
            "SHANGHAI",
            "TIANJIN",
            "CHONGQING",
        ]

    @pytest.mark.parametrize(
        "invalid_province",
        [
            "INVALID_PROVINCE",
            "",
            "NONEXISTENT_PLACE",
        ],
    )
    def test_generate_random_address_invalid_provinces(self, invalid_province):
        """测试无效省份时的回退处理 - 参数化测试覆盖多种边界情况"""
        with patch("app.utils.address_tools.logger") as mock_logger:
            address = generate_random_address(invalid_province)
            assert "Guangdong Province" in address  # 默认回退
            mock_logger.warning.assert_called()

    def test_generate_random_address_none_input_raises_exception(self):
        """测试 None 输入应该抛出异常 - 类型安全测试"""
        # 根据函数签名 (place_of_birth: str)，None 不是有效输入
        # 应该抛出 AttributeError: 'NoneType' object has no attribute 'strip'
        with pytest.raises(
            AttributeError, match="'NoneType' object has no attribute 'strip'"
        ):
            generate_random_address(None)  # type: ignore[arg-type] # 故意的无效输入测试

    def test_china_province_city_map_structure(self):
        """测试省市映射数据结构完整性"""
        assert isinstance(CHINA_PROVINCE_CITY_MAP, dict)
        assert len(CHINA_PROVINCE_CITY_MAP) > 0

        # 检查关键省份存在
        key_provinces = ["GUANGDONG", "BEIJING", "SHANGHAI"]
        for province in key_provinces:
            if province in CHINA_PROVINCE_CITY_MAP:
                assert isinstance(CHINA_PROVINCE_CITY_MAP[province], list)
                assert len(CHINA_PROVINCE_CITY_MAP[province]) > 0


class TestDateUtils:
    """现代化日期计算工具测试 - 使用 freezegun 替代复杂的 mock"""

    @pytest.mark.parametrize(
        "start_date, working_days, expected_date",
        [
            ("2025-01-06", 5, "2025-01-13"),  # Monday + 5 days = next Monday
            ("2025-01-06", 0, "2025-01-06"),  # Monday + 0 days = same day
            ("2025-01-06", 1, "2025-01-07"),  # Monday + 1 day = Tuesday
            ("2025-01-10", 1, "2025-01-13"),  # Friday + 1 day = next Monday
        ],
    )
    def test_calculate_working_days_parametrized(
        self, start_date, working_days, expected_date
    ):
        """测试工作日计算 - 参数化测试覆盖多种场景"""
        with freeze_time(start_date):
            result = calculate_working_days_from_today(working_days)
            expected = datetime.datetime.strptime(expected_date, "%Y-%m-%d").date()
            assert result == expected

    @freeze_time("2025-01-06")  # Monday
    def test_calculate_working_days_with_holidays(self):
        """测试包含节假日的工作日计算 - 使用 freezegun 简化时间控制"""
        holidays = ["07/01/2025", "08/01/2025"]  # Tuesday and Wednesday
        result = calculate_working_days_from_today(3, holidays)
        expected = datetime.date(2025, 1, 13)  # Skips holidays
        assert result == expected

    def test_vietnam_holidays_2025_format(self):
        """测试越南假日列表格式"""
        assert isinstance(VIETNAM_HOLIDAYS_2025, list)
        assert len(VIETNAM_HOLIDAYS_2025) > 0

        # 检查日期格式
        for holiday in VIETNAM_HOLIDAYS_2025:
            assert isinstance(holiday, str)
            parts = holiday.split("/")
            assert len(parts) == 3
            # 应该是 DD/MM/YYYY 格式
            assert 1 <= int(parts[0]) <= 31  # Day
            assert 1 <= int(parts[1]) <= 12  # Month
            assert int(parts[2]) == 2025  # Year


class TestCaptchaSolver:
    """验证码解析器测试"""

    def test_captcha_solver_init_with_api_key(self):
        """测试验证码解析器初始化"""
        with patch.dict(os.environ, {"ANTI_CAPTCHA_API_KEY": "test_key"}):
            solver = CaptchaSolver()

            assert solver.api_key == "test_key"
            assert solver.max_attempts == 20
            assert solver.polling_interval == 0.3

    def test_captcha_solver_init_without_api_key(self):
        """测试无API密钥时的初始化"""
        with patch.dict(os.environ, {}, clear=True):
            with patch("app.utils.captcha_solver.logger") as mock_logger:
                solver = CaptchaSolver()

                assert solver.api_key is None
                mock_logger.error.assert_called()

    @patch("app.utils.captcha_solver.imagecaptcha")
    def test_solve_captcha_success(self, mock_imagecaptcha_class):
        """测试成功解析验证码"""
        with patch.dict(os.environ, {"ANTI_CAPTCHA_API_KEY": "test_key"}):
            # Mock SDK实例
            mock_solver = Mock()
            mock_solver.solve_and_return_solution.return_value = "123456"
            mock_imagecaptcha_class.return_value = mock_solver

            solver = CaptchaSolver()
            mock_page = Mock()
            mock_page.is_visible.return_value = False

            result = solver.solve_captcha("test_image.png", mock_page)

            assert result == "123456"
            assert solver.last_solution == "123456"
            # 验证SDK方法被正确调用
            mock_solver.set_key.assert_called_with("test_key")
            mock_solver.set_numeric.assert_called_with(1)
            mock_solver.solve_and_return_solution.assert_called_with("test_image.png")

    @patch("app.utils.captcha_solver.imagecaptcha")
    def test_solve_captcha_api_error(self, mock_imagecaptcha_class):
        """测试API错误响应"""
        with patch.dict(os.environ, {"ANTI_CAPTCHA_API_KEY": "test_key"}):
            # Mock SDK实例返回错误（0表示失败）
            mock_solver = Mock()
            mock_solver.solve_and_return_solution.return_value = 0
            mock_solver.error_code = "ERROR_KEY_DOES_NOT_EXIST"
            mock_imagecaptcha_class.return_value = mock_solver

            solver = CaptchaSolver()
            solver.max_attempts = 1  # 设置只尝试1次，避免重试覆盖错误信息
            mock_page = Mock()
            mock_page.is_visible.return_value = False

            result = solver.solve_captcha("test_image.png", mock_page)

            assert result is None
            # 最终错误是超时消息（这是设计行为）
            assert (
                solver.last_error is not None
                and "达到最大尝试次数" in solver.last_error
            )

    def test_solve_captcha_no_api_key(self):
        """测试无API密钥时的处理"""
        with patch.dict(os.environ, {}, clear=True):
            solver = CaptchaSolver()
            mock_page = Mock()

            result = solver.solve_captcha("test_image.png", mock_page)

            assert result is None

    @patch("app.utils.captcha_solver.imagecaptcha")
    def test_solve_captcha_file_not_found(self, mock_imagecaptcha_class):
        """测试图片文件不存在时的处理"""
        with patch.dict(os.environ, {"ANTI_CAPTCHA_API_KEY": "test_key"}):
            # Mock SDK抛出文件不存在异常
            mock_solver = Mock()
            mock_solver.solve_and_return_solution.side_effect = FileNotFoundError(
                "File not found"
            )
            mock_imagecaptcha_class.return_value = mock_solver

            solver = CaptchaSolver()
            mock_page = Mock()
            mock_page.is_visible.return_value = False

            result = solver.solve_captcha("nonexistent.png", mock_page)

            assert result is None
            assert solver.last_error is not None

    def test_standalone_solve_captcha_function(self):
        """测试独立的solve_captcha函数"""
        with patch("app.utils.captcha_solver.CaptchaSolver") as mock_solver_class:
            mock_solver = Mock()
            mock_solver.solve_captcha.return_value = "654321"
            mock_solver_class.return_value = mock_solver

            mock_page = Mock()
            result = solve_captcha("test.png", mock_page)

            assert result == "654321"
            mock_solver_class.assert_called_once()
            mock_solver.solve_captcha.assert_called_once_with("test.png", mock_page)


class TestEnvLoader:
    """环境变量加载器测试"""

    def test_load_credit_cards_from_env_single_card(self):
        """测试从环境变量加载单张信用卡"""
        env_vars = {
            "CREDIT_CARD_1_TYPE": "Visa",
            "CREDIT_CARD_1_NUMBER": "****************",
            "CREDIT_CARD_1_FIRST_NAME": "John",
            "CREDIT_CARD_1_LAST_NAME": "Doe",
            "CREDIT_CARD_1_BILLING_ADDRESS": "123 Main St",
            "CREDIT_CARD_1_CITY": "New York",
            "CREDIT_CARD_1_COUNTRY": "USA",
            "CREDIT_CARD_1_EXP_MONTH": "12",
            "CREDIT_CARD_1_EXP_YEAR": "2025",
            "CREDIT_CARD_1_CVV": "123",
        }

        with patch.dict(os.environ, env_vars):
            cards = load_credit_cards_from_env()

            assert len(cards) == 1
            card = cards[0]
            assert card["card_type"] == "Visa"
            assert card["card_number"] == "****************"
            assert card["first_name"] == "John"

    def test_load_credit_cards_from_env_multiple_cards(self):
        """测试从环境变量加载多张信用卡"""
        env_vars = {
            "CREDIT_CARD_1_TYPE": "Visa",
            "CREDIT_CARD_1_NUMBER": "****************",
            "CREDIT_CARD_1_FIRST_NAME": "John",
            "CREDIT_CARD_1_LAST_NAME": "Doe",
            "CREDIT_CARD_1_BILLING_ADDRESS": "123 Main St",
            "CREDIT_CARD_1_CITY": "New York",
            "CREDIT_CARD_1_COUNTRY": "USA",
            "CREDIT_CARD_1_EXP_MONTH": "12",
            "CREDIT_CARD_1_EXP_YEAR": "2025",
            "CREDIT_CARD_1_CVV": "123",
            "CREDIT_CARD_2_TYPE": "MasterCard",
            "CREDIT_CARD_2_NUMBER": "****************",
            "CREDIT_CARD_2_FIRST_NAME": "Jane",
            "CREDIT_CARD_2_LAST_NAME": "Smith",
            "CREDIT_CARD_2_BILLING_ADDRESS": "456 Oak Ave",
            "CREDIT_CARD_2_CITY": "Los Angeles",
            "CREDIT_CARD_2_COUNTRY": "USA",
            "CREDIT_CARD_2_EXP_MONTH": "06",
            "CREDIT_CARD_2_EXP_YEAR": "2026",
            "CREDIT_CARD_2_CVV": "456",
        }

        with patch.dict(os.environ, env_vars):
            cards = load_credit_cards_from_env()

            assert len(cards) == 2
            assert cards[0]["card_type"] == "Visa"
            assert cards[1]["card_type"] == "MasterCard"

    def test_load_credit_cards_from_env_missing_number(self):
        """测试缺少卡号时的处理"""
        env_vars = {
            "CREDIT_CARD_1_TYPE": "Visa",
            "CREDIT_CARD_1_FIRST_NAME": "John",
            "CREDIT_CARD_1_LAST_NAME": "Doe",
            # 缺少 CREDIT_CARD_1_NUMBER
        }

        with patch.dict(os.environ, env_vars):
            with patch("app.utils.env_loader.logger") as mock_logger:
                cards = load_credit_cards_from_env()

                assert len(cards) == 0
                mock_logger.warning.assert_called()

    def test_load_credit_cards_from_env_empty(self):
        """测试无信用卡配置时的处理"""
        with patch.dict(os.environ, {}, clear=True):
            cards = load_credit_cards_from_env()

            assert len(cards) == 0


class TestLoggerConfig:
    """日志配置测试"""

    def test_get_logger(self):
        """测试获取日志记录器"""
        logger = get_logger()

        assert logger is not None
        assert hasattr(logger, "info")
        assert hasattr(logger, "error")
        assert hasattr(logger, "warning")

    def test_setup_logger_basic(self):
        """测试基本的日志记录器设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch("app.utils.logger_config.logger") as mock_logger:
                # 调用setup_logger，使用正确的参数
                setup_logger(
                    log_dir=temp_dir,
                    console_level="INFO",
                    file_level="DEBUG",
                    log_filename_prefix="test_app",
                    user_display_timezone="Asia/Shanghai",
                )

                # 验证logger配置被调用
                mock_logger.remove.assert_called()
                mock_logger.add.assert_called()


class TestSecurityUtils:
    """
    现代化安全工具测试 - 重大改进！

    将 8 个分离的测试函数合并为 3 个参数化测试，
    覆盖所有相同场景并增加更多边界情况。
    """

    @pytest.mark.parametrize(
        "input_str, visible_chars, expected",
        [
            # 基本情况
            ("1234567890", 4, "******7890"),
            ("1234567890", 2, "********90"),
            ("123", 4, "***"),
            ("12", 4, "**"),
            ("", 4, ""),
            # 边界情况
            ("a", 1, "*"),  # 长度 <= show_chars 时全部掩码
            ("ab", 1, "*b"),
            ("password123", 3, "********123"),
            ("short", 10, "*****"),  # 长度 <= show_chars 时全部掩码
            ("test", 0, "****test"),  # show_chars = 0 时在前面加掩码
        ],
    )
    def test_mask_sensitive_data_all_cases(self, input_str, visible_chars, expected):
        """测试敏感数据掩码 - 参数化测试覆盖所有场景"""
        if visible_chars == 4:  # 测试默认参数
            result = mask_sensitive_data(input_str)
        else:
            result = mask_sensitive_data(input_str, visible_chars)
        assert result == expected

    @pytest.mark.parametrize(
        "card_number, expected",
        [
            # 标准情况
            ("1234567890123456", "**** **** **** 3456"),
            ("1234 5678 9012 3456", "**** **** **** 3456"),  # 带空格
            ("123", "***"),
            ("", ""),
            # 边界情况
            ("12345", "*234 5"),  # 5位数会按4位分组
            ("1", "*"),
            # 真实银行卡号
            ("****************", "**** **** **** 1111"),  # Visa
            ("****************", "**** **** **** 4444"),  # Mastercard
        ],
    )
    def test_mask_card_number_all_cases(self, card_number, expected):
        """测试银行卡号掩码 - 参数化测试覆盖所有场景"""
        result = mask_card_number(card_number)
        assert result == expected

    @pytest.mark.parametrize(
        "email, expected",
        [
            # 有效邮箱
            ("<EMAIL>", "t***@example.com"),
            ("<EMAIL>", "<EMAIL>"),  # 单字符用户名特殊处理
            ("<EMAIL>", "u***@domain.org"),
            (
                "<EMAIL>",
                "l*****************@company.com",
            ),  # 长用户名全部掩码
            # 边界情况
            ("", ""),
            ("invalid_email", "invalid_email"),
            ("@domain.com", "@domain.com"),
            ("user@", "u***@"),  # 即使没有域名也会掩码用户名
        ],
    )
    def test_mask_email_all_cases(self, email, expected):
        """测试邮箱掩码 - 参数化测试覆盖所有场景"""
        result = mask_email(email)
        assert result == expected


class TestUtilsIntegration:
    """工具模块集成测试"""

    def test_address_generation_with_logging(self):
        """测试地址生成与日志记录的集成"""
        get_logger()

        # 不需要传递参数给get_logger
        address = generate_random_address("GUANGDONG")

        assert address is not None
        assert "Province, China" in address

    def test_captcha_solver_with_logging(self):
        """测试验证码解析器与日志记录的集成"""
        with patch.dict(os.environ, {"ANTI_CAPTCHA_API_KEY": "test_key"}):
            solver = CaptchaSolver()

            # 测试获取错误信息
            solver.last_error = "Test error"
            assert solver.get_last_error() == "Test error"

            # 测试获取解决方案
            solver.last_solution = "123456"
            assert solver.get_last_solution() == "123456"

            # 测试获取任务ID
            solver.last_task_id = 12345
            assert solver.get_last_task_id() == 12345

    def test_date_calculation_with_vietnam_holidays(self):
        """测试使用越南节假日的日期计算"""
        with patch("app.utils.date_utils.datetime") as mock_datetime:
            mock_today = datetime.date(2025, 1, 1)  # 元旦

            # 创建一个mock的datetime对象
            mock_now = Mock()
            mock_now.date.return_value = mock_today
            mock_datetime.datetime.now.return_value = mock_now

            # 确保其他datetime方法正常工作
            mock_datetime.datetime.strptime = datetime.datetime.strptime
            mock_datetime.timedelta = datetime.timedelta

            result = calculate_working_days_from_today(1, VIETNAM_HOLIDAYS_2025)

            # 由于1月1日是元旦假期，1个工作日后应该是1月2日
            expected = datetime.date(2025, 1, 2)
            assert result == expected

    def test_env_loader_with_security(self):
        """测试环境变量加载与安全功能的集成"""
        # 设置包含敏感信息的环境变量
        env_vars = {
            "CREDIT_CARD_1_TYPE": "Visa",
            "CREDIT_CARD_1_NUMBER": "****************",
            "CREDIT_CARD_1_FIRST_NAME": "John",
            "CREDIT_CARD_1_LAST_NAME": "Doe",
            "CREDIT_CARD_1_BILLING_ADDRESS": "123 Main St",
            "CREDIT_CARD_1_CITY": "New York",
            "CREDIT_CARD_1_COUNTRY": "USA",
            "CREDIT_CARD_1_EXP_MONTH": "12",
            "CREDIT_CARD_1_EXP_YEAR": "2025",
            "CREDIT_CARD_1_CVV": "123",
        }

        with patch.dict(os.environ, env_vars):
            # 测试加载信用卡信息
            cards = load_credit_cards_from_env()
            assert len(cards) == 1

            # 测试对信用卡号进行掩码处理
            card_number = cards[0]["card_number"]
            masked_card = mask_card_number(card_number)
            assert masked_card == "**** **** **** 9012"

            # 测试对CVV进行掩码处理
            cvv = cards[0]["cvv"]
            masked_cvv = mask_sensitive_data(cvv)
            assert masked_cvv == "***"  # 对于3位的CVV，完全掩码
