# backend/routes/visa/file_handling.py
"""
签证申请文件处理和工具功能模块

遵循PP和QQ Notepad要求：
- 单一职责：专注于文件处理和工具函数
- API兼容性：保持现有接口完全兼容
- 异常处理：全链路日志与异常兜底
- 工具函数：重复检查、日期计算等
"""

from datetime import datetime

from fastapi import APIRouter
from sqlalchemy import select

from app.data.models.applicant import Applicant
from app.data.models.application import Application
from app.data.models.order import Order
from app.utils.date_utils import (
    VIETNAM_HOLIDAYS_2025,
    calculate_working_days_from_today,
)
from app.utils.logger_config import get_logger
from backend.db_config.unified_connection import get_unified_db
from backend.models.order import OrderStatus

logger = get_logger()

router = APIRouter()


@router.get("/calculate-expedited-date")
async def get_expedited_date(days: int = 4):
    """
    计算出签生效日期（从今天起第N个工作日）

    - **days**: 工作日数量 (1, 2, 3 或 4，默认 4)
    - **返回**: 计算后的出签生效日期和相关信息
    """
    try:
        logger.info(f"🗓️ 开始计算加急日期: {days}个工作日")

        # 验证days参数
        if days not in [1, 2, 3, 4]:
            logger.warning(f"⚠️ days参数无效: {days}, 使用默认值4")
            days = 4  # 默认4工作日

        # 计算指定工作日后的日期
        expedited_date = calculate_working_days_from_today(days, VIETNAM_HOLIDAYS_2025)

        description_map = {
            1: "从今天起第1个工作日（跳过周末和越南节假日）",
            2: "从今天起第2个工作日（跳过周末和越南节假日）",
            3: "从今天起第3个工作日（跳过周末和越南节假日）",
            4: "从今天起第4个工作日（跳过周末和越南节假日）",
        }

        # 🔥 返回与原始visa.py完全兼容的字典格式
        response = {
            "success": True,
            "expedited_date": expedited_date.strftime("%d/%m/%Y"),
            "calculation_info": {
                "working_days": days,
                "calculated_from": "today",
                "timezone": "UTC",
                "description": description_map.get(days, f"从今天起第{days}个工作日"),
            },
        }

        logger.info(f"✅ 加急日期计算成功: {expedited_date.strftime('%d/%m/%Y')}")
        return response

    except Exception as e:
        logger.error(f"❌ 计算出签生效日期失败: {str(e)}", exc_info=True)
        # 🔥 返回与原始visa.py完全兼容的错误格式
        return {"success": False, "error": str(e), "expedited_date": None}


@router.get("/check-duplicate/{passport_number}")
async def check_duplicate_submission(passport_number: str):
    """
    检查是否存在重复提交

    🔥 基于修复后的订单系统：
    - 每次提交都会生成新订单
    - 重复检查基于业务逻辑：是否已有成功/处理中的申请
    - 失败的申请可以重新提交，无需弹窗

    - **passport_number**: 护照号码
    - **返回**: 是否存在重复提交的信息

    业务逻辑（基于订单状态）：
    1. 失败订单(status=failed) - 可直接重新提交，无需弹窗
    2. 创建中订单(status=created) - 可直接重新提交，无需弹窗
    3. 成功订单(status=success且有越南申请编号) - 需要弹窗提醒
    4. 提交中订单(status=pending_*) - 需要弹窗提醒
    """
    try:
        logger.info(f"🔍 开始检查重复提交（基于订单系统）: {passport_number}")

        # 🔥 查询该护照号的所有订单，按创建时间倒序
        # from app.services.order_service import OrderService  # 未使用，已注释

        # 获取统一数据库连接
        unified_db = await get_unified_db()
        # order_service = OrderService(unified_db)  # 未使用，已注释

        # 查询该护照号的最新订单（通过 Order → Application → Applicant 关系）
        async with unified_db.get_session() as session:
            result = await session.execute(
                select(Order)
                .join(Application, Order.id == Application.order_id)
                .join(Applicant, Application.applicant_id == Applicant.id)
                .where(Applicant.passport_number == passport_number.strip().upper())
                .where(Order.order_status == OrderStatus.CREATED)  # 只查询已创建的订单
                .order_by(Order.created_at.desc())
                .limit(1)
            )
            latest_valid_order = result.scalar_one_or_none()

        if latest_valid_order:
            # 找到已创建的订单，检查是否有关联的申请数据
            order_status = latest_valid_order.order_status

            # 获取关联的申请数据
            async with unified_db.get_session() as session:
                app_result = await session.execute(
                    select(Application).where(
                        Application.order_id == latest_valid_order.id
                    )
                )
                application = app_result.scalar_one_or_none()

            # 检查是否有越南申请编号
            vietnam_application_number = None
            if application and application.application_number:
                vietnam_application_number = application.application_number
                warning_type = "success"
                can_resubmit = False  # 已有申请编号，需要弹窗确认
            else:
                warning_type = "pending"
                can_resubmit = True  # 订单存在但无申请编号，可以重新提交

            response = {
                "success": True,
                "exists": True,
                "can_resubmit": can_resubmit,
                "warning_type": warning_type,
                "application_number": vietnam_application_number
                or latest_valid_order.order_no,
                "submission_time": latest_valid_order.created_at.strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                if latest_valid_order.created_at
                else None,
                "status": order_status.value
                if hasattr(order_status, "value")
                else str(order_status),
                "approval_status": "WAITING",  # 暂时保持兼容性
                "order_no": latest_valid_order.order_no,
            }

            logger.info(
                f"✅ 检查结果（基于订单系统）{passport_number}: can_resubmit={can_resubmit}, warning_type={warning_type}, order_no={latest_valid_order.order_no}"
            )
        else:
            # 没有有效订单记录，或者只有失败/创建中的订单，可以直接提交
            response = {
                "success": True,
                "exists": False,
                "can_resubmit": True,
                "warning_type": "none",
            }

            logger.info(f"✅ 无重复提交风险（基于订单系统）: {passport_number}")

        return response

    except Exception as e:
        logger.error(
            f"❌ 检查重复提交失败（基于订单系统）: {passport_number} - {str(e)}",
            exc_info=True,
        )
        # 出错时默认允许提交
        return {
            "success": False,
            "exists": False,
            "can_resubmit": True,
            "warning_type": "error",
            "error": str(e),
        }


def parse_validity_days(validity_duration: str) -> int:
    """
    解析签证有效期天数

    Args:
        validity_duration: 签证有效期字符串

    Returns:
        int: 有效期天数
    """
    if not validity_duration:
        return 30

    # 提取数字
    import re

    numbers = re.findall(r"\d+", validity_duration)
    if numbers:
        return int(numbers[0])
    return 30


async def validate_file_upload(
    file_content: bytes, filename: str, content_type: str
) -> bool:
    """
    验证上传文件的有效性

    Args:
        file_content: 文件内容
        filename: 文件名
        content_type: 文件类型

    Returns:
        bool: 是否有效
    """
    try:
        # 检查文件大小（最大5MB）
        max_size = 5 * 1024 * 1024  # 5MB
        if len(file_content) > max_size:
            logger.warning(f"⚠️ 文件过大: {filename} - {len(file_content)} bytes")
            return False

        # 检查文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png"]
        if content_type not in allowed_types:
            logger.warning(f"⚠️ 文件类型不支持: {filename} - {content_type}")
            return False

        # 检查文件内容（基本验证）
        if len(file_content) < 100:  # 文件内容太小
            logger.warning(f"⚠️ 文件内容可能无效: {filename}")
            return False

        logger.info(f"✅ 文件验证通过: {filename}")
        return True

    except Exception as e:
        logger.error(f"❌ 文件验证失败: {filename} - {str(e)}")
        return False


async def generate_unique_filename(original_filename: str, prefix: str = "") -> str:
    """
    生成唯一文件名

    Args:
        original_filename: 原始文件名
        prefix: 文件名前缀

    Returns:
        str: 唯一文件名
    """
    try:
        from pathlib import Path
        import uuid

        # 获取文件扩展名
        file_extension = Path(original_filename).suffix

        # 生成唯一ID
        unique_id = str(uuid.uuid4())[:8]

        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 组合文件名
        unique_filename = f"{prefix}{timestamp}_{unique_id}{file_extension}"

        logger.info(f"✅ 生成唯一文件名: {original_filename} -> {unique_filename}")
        return unique_filename

    except Exception as e:
        logger.error(f"❌ 生成唯一文件名失败: {original_filename} - {str(e)}")
        return f"{prefix}{datetime.now().strftime('%Y%m%d_%H%M%S')}_{original_filename}"


def format_applicant_name(
    surname: str | None, given_name: str | None, chinese_name: str | None
) -> str:
    """
    格式化申请人姓名

    Args:
        surname: 姓
        given_name: 名
        chinese_name: 中文姓名

    Returns:
        str: 格式化后的姓名
    """
    try:
        # 构建英文姓名
        english_name = f"{surname or ''} {given_name or ''}".strip()

        # 如果有中文姓名，添加到英文姓名后面
        if chinese_name:
            if english_name:
                formatted_name = f"{english_name} ({chinese_name})"
            else:
                formatted_name = chinese_name
        else:
            formatted_name = english_name or "Unknown"

        logger.debug(f"✅ 格式化姓名: {formatted_name}")
        return formatted_name

    except Exception as e:
        logger.error(f"❌ 格式化姓名失败: {str(e)}")
        return "Unknown"


def parse_date_string(
    date_str: str | None, format_hint: str = "auto"
) -> datetime | None:
    """
    解析日期字符串

    Args:
        date_str: 日期字符串
        format_hint: 格式提示 ("auto", "dd/mm/yyyy", "yyyy-mm-dd")

    Returns:
        Optional[datetime]: 解析后的日期对象
    """
    if not date_str:
        return None

    try:
        if (format_hint == "auto" or format_hint == "dd/mm/yyyy") and "/" in date_str:
            # DD/MM/YYYY format
            day, month, year = date_str.split("/")
            return datetime(int(year), int(month), int(day))

        if (format_hint == "auto" or format_hint == "yyyy-mm-dd") and "-" in date_str:
            # YYYY-MM-DD format
            return datetime.fromisoformat(date_str)

        # 尝试其他常见格式
        formats = [
            "%Y-%m-%d",
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%Y/%m/%d",
            "%d-%m-%Y",
            "%Y.%m.%d",
            "%d.%m.%Y",
        ]

        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        logger.warning(f"⚠️ 无法解析日期格式: {date_str}")
        return None

    except Exception as e:
        logger.error(f"❌ 日期解析失败: {date_str} - {str(e)}")
        return None
