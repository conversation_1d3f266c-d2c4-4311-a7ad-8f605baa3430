/**
 * 测试工具：会话失效处理
 *
 * 用于处理e2e测试中的会话失效弹窗
 */

import type { Page } from '@playwright/test'

/**
 * 检查并处理会话失效弹窗
 * @param page Playwright页面对象
 * @returns 如果检测到会话失效弹窗返回true，否则返回false
 */
export async function handleSessionInvalidatedDialog(page: Page): Promise<boolean> {
  try {
    const sessionDialog = page.locator('[aria-label="重复登录提醒"]')
    if (await sessionDialog.isVisible({ timeout: 2000 })) {
      console.log('⚠️ 检测到会话失效弹窗，点击确认')
      await page.locator('button').filter({ hasText: '重新登录' }).click()
      await page.waitForTimeout(1000)
      console.log('⚠️ 会话失效，测试终止')
      return true
    }
    return false
  } catch (error) {
    console.log('⚠️ 弹窗检测异常:', error)
    return false
  }
}

/**
 * 在执行操作前检查会话失效弹窗
 * 如果检测到弹窗，会抛出错误终止测试
 * @param page Playwright页面对象
 */
export async function checkSessionBeforeAction(page: Page): Promise<void> {
  const sessionInvalidated = await handleSessionInvalidatedDialog(page)
  if (sessionInvalidated) {
    throw new Error('会话失效，测试终止')
  }
}

/**
 * 安全点击按钮（会先检查会话失效弹窗）
 * @param page Playwright页面对象
 * @param buttonSelector 按钮选择器
 */
export async function safeClickButton(page: Page, buttonSelector: string): Promise<void> {
  await checkSessionBeforeAction(page)

  const button = page.locator(buttonSelector)
  await button.click()
}
