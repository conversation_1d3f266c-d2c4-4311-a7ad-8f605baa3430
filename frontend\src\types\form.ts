// 严格按照旧版项目字段定义的类型接口 - 不得更改字段名
export interface PersonalInfo {
  // 基本个人信息 - 字段名严格对应旧版
  surname: string // 姓氏
  given_name: string // 名字
  chinese_name?: string // 中文名（可选）
  sex: 'M' | 'F' | '' // 性别（允许空字符串用于初始状态）
  dob: string // 出生日期 DD/MM/YYYY
  place_of_birth: string // 出生地
  nationality: string // 国籍
  religion: string // 宗教（表单字段，非OCR识别）
}

export interface PassportInfo {
  // 护照信息 - 字段名严格对应旧版
  passport_number: string // 护照号码
  date_of_issue: string // 签发日期 DD/MM/YYYY
  place_of_issue: string // 签发地
  passport_expiry: string // 到期日期 DD/MM/YYYY
  passport_type: string // 护照类型（默认：Ordinary passport）
}

// OCR识别结果类型 - 严格对应旧版护照OCR实际字段，不包含宗教等非护照信息
export interface OCRPassportData {
  // 护照上实际存在的个人信息字段
  surname?: string // 姓氏
  given_name?: string // 名字
  chinese_name?: string // 中文姓名
  sex?: 'M' | 'F' // 性别
  date_of_birth?: string // 出生日期（OCR识别用）
  place_of_birth?: string // 出生地
  nationality?: string // 国籍

  // 护照信息字段
  passport_number?: string // 护照号码
  date_of_issue?: string // 签发日期（OCR识别用）
  date_of_expiry?: string // 到期日期（OCR识别用）
  place_of_issue?: string // 签发地
}

export interface ContactInfo {
  // 联系信息 - 字段名严格对应旧版
  email: string // 邮箱
  telephone_number: string // 电话号码
  permanent_address?: string // 永久地址
  contact_address?: string // 联系地址
  emergency_contact_name?: string // 紧急联系人姓名
  emergency_contact_phone?: string // 紧急联系人电话
  emergency_address?: string // 紧急联系地址
}

export interface VisaInfo {
  // 签证信息 - 字段名严格对应旧版
  visa_entry_type: 'Single-entry' | 'Multiple-entry' | '' // 入境类型（允许空字符串用于初始状态）
  visa_validity_duration: '30天' | '90天' | '' // 有效期（允许空字符串用于初始状态）
  visa_start_date: string // 生效日期 DD/MM/YYYY
  intended_entry_gate: string // 入境口岸
  expedited_type?: '4days' | '3days' | '2days' | '1days' // 加急类型
  purpose_of_entry: 'Tourist' | 'Business' | 'Visiting relatives' | 'Working' | 'Other' // 入境目的
  customer_source?: string // 客户来源
}

export interface PreviousVisitInfo {
  // 上次访问信息 - 字段名严格对应旧版
  visited_vietnam_last_year?: boolean // 是否去年访问过越南（允许undefined用于初始状态）
  previous_entry_date?: string // 上次入境日期 DD/MM/YYYY
  previous_exit_date?: string // 上次出境日期 DD/MM/YYYY
  previous_purpose?: string // 上次访问目的
}

export interface VietnamContactInfo {
  // 越南联系组织信息
  has_vietnam_contact?: boolean // 是否有越南联系组织（允许undefined用于初始状态）
  vietnam_contact_organization?: string // 联系组织名称
  vietnam_contact_phone?: string // 联系电话
  vietnam_contact_address?: string // 联系地址
  vietnam_contact_purpose?: string // 联系目的
}

export interface FileUploads {
  // 文件上传 - 字段名严格对应旧版
  portrait_photo?: File // 证件照
  passport_scan?: File // 护照扫描件
}

// 提交记录类型
export interface SubmissionRecord {
  id: string // 护照号码（兼容性保留）
  orderNo?: string // 新增：订单编号（如 VN20250602RL34XY）
  status: 'submitting' | 'success' | 'failed'
  applicantName: string // 完整申请人姓名（包含中英文）
  chinese_name?: string // 中文姓名（用于显示）
  submitTime: Date
}

// 完整表单数据 - 严格按照旧版结构
export interface VisaFormData
  extends PersonalInfo,
    PassportInfo,
    ContactInfo,
    VisaInfo,
    PreviousVisitInfo,
    VietnamContactInfo {
  files?: FileUploads
}

// 验证规则类型 - 对应旧版 validationRules
export interface ValidationRule {
  required?: boolean
  minLength?: number
  pattern?: RegExp
  message: string
}

export interface ValidationRules {
  [key: string]: ValidationRule
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// 文件预览类型
export interface FilePreview {
  file: File
  url: string
  name: string
  size: number
}

// OCR识别结果类型 - 严格对应旧版OCR功能，只包含护照实际字段
export interface OCRResult {
  success: boolean
  data?: OCRPassportData // 使用专门的OCR数据类型
  message?: string
}

// 表单状态类型
export interface FormState {
  isLoading: boolean
  isSubmitting: boolean
  hasErrors: boolean
  isDirty: boolean
  currentStep: number
}

// 验证错误类型
export interface ValidationError {
  field: string
  message: string
}

export interface FormValidation {
  isValid: boolean
  errors: ValidationError[]
}
